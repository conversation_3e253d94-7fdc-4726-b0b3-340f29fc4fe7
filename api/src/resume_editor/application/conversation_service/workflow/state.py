from langgraph.graph import MessagesState


class ResumeEditorState(MessagesState):
    """State class for the Resume Editor LangGraph workflow.

    It keeps track of the information necessary to maintain a coherent
    editing conversation between the Resume Editor and the user, with multi-user support.

    Attributes:
        user_id (str): Unique identifier for the user (for multi-user support).
        editor_id (str): Unique identifier for the resume editor type.
        editor_name (str): The name of the resume editor.
        editor_specialty (str): The specialty area of the resume editor.
        editor_approach (str): The editing approach and methodology.
        editor_focus_areas (str): The key focus areas of the editor.
        summary (str): A summary of the conversation. This is used to reduce token usage.
        session_goals (list): Goals set for the current editing session.
        user_context (str): Additional context about the user's career situation.
        resume_data (dict): Current resume data for the user.
    """

    user_id: str
    editor_id: str
    editor_name: str
    editor_specialty: str
    editor_approach: str
    editor_focus_areas: str
    summary: str
    session_goals: list
    user_context: str
    resume_data: dict


def state_to_str(state: ResumeEditorState) -> str:
    """Convert ResumeEditorState to string representation for logging/debugging."""
    if "summary" in state and bool(state["summary"]):
        conversation = state["summary"]
    elif "messages" in state and bool(state["messages"]):
        conversation = state["messages"]
    else:
        conversation = ""

    return f"""
ResumeEditorState(
    user_id={state["user_id"]},
    editor_id={state["editor_id"]},
    editor_name={state["editor_name"]},
    editor_specialty={state["editor_specialty"]},
    editor_approach={state["editor_approach"]},
    editor_focus_areas={state["editor_focus_areas"]},
    session_goals={state.get("session_goals", [])},
    conversation={conversation}
)
    """
