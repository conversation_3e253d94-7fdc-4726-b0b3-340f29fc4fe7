from loguru import logger
from langchain_core.documents import Document

from common.application.rag.retriever import get_retriever
from common.application.rag.splitter import get_splitter
from common.infrastructure.mongo.client import MongoClientWrapper
from common.infrastructure.mongo.index import MongoIndex
from resume_editor.config import settings


class ResumeEditorLongTermMemoryCreator:
    """Creates and manages long-term memory for resume editors."""
    
    def __init__(self, retriever, splitter) -> None:
        self.retriever = retriever
        self.splitter = splitter

    @classmethod
    def build_from_settings(cls) -> "ResumeEditorLongTermMemoryCreator":
        """Build the memory creator from configuration settings."""
        retriever = get_retriever(
            embedding_model_id=settings.RAG_TEXT_EMBEDDING_MODEL_ID,
            mongo_uri=settings.MONGO_URI,
            db_name=settings.MONGO_DB_NAME,
            collection_name=settings.MONGO_RESUME_LONG_TERM_MEMORY_COLLECTION,
            k=settings.RAG_TOP_K,
            device=settings.RAG_DEVICE,
        )
        splitter = get_splitter(chunk_size=settings.RAG_CHUNK_SIZE)

        return cls(retriever, splitter)

    def create_memory_from_documents(self, documents: list[Document]) -> None:
        """Create long-term memory from a list of documents.
        
        Args:
            documents: List of documents to process and store
        """
        if len(documents) == 0:
            logger.warning("No documents to process for resume editor memory. Exiting.")
            return

        # First clear the long term memory collection to avoid duplicates.
        with MongoClientWrapper(
            model=Document, 
            collection_name=settings.MONGO_RESUME_LONG_TERM_MEMORY_COLLECTION,
            database_name=settings.MONGO_DB_NAME,
            mongodb_uri=settings.MONGO_URI,
            app_name="resume_editor",
        ) as client:
            client.clear_collection()

        # Process documents
        chunked_docs = self.splitter.split_documents(documents)
        
        # Add documents to vector store
        self.retriever.vectorstore.add_documents(chunked_docs)
        
        # Create search index
        self.__create_index()

    def __create_index(self) -> None:
        """Create the search index for the memory collection."""
        with MongoClientWrapper(
            model=Document, 
            collection_name=settings.MONGO_RESUME_LONG_TERM_MEMORY_COLLECTION,
            database_name=settings.MONGO_DB_NAME,
            mongodb_uri=settings.MONGO_URI,
            app_name="resume_editor",
        ) as client:
            self.index = MongoIndex(
                retriever=self.retriever,
                mongodb_client=client,
            )
            self.index.create(
                is_hybrid=True, embedding_dim=settings.RAG_TEXT_EMBEDDING_MODEL_DIM
            )


class ResumeEditorLongTermMemoryRetriever:
    """Retrieves information from resume editor long-term memory."""
    
    def __init__(self, retriever) -> None:
        self.retriever = retriever

    @classmethod
    def build_from_settings(cls) -> "ResumeEditorLongTermMemoryRetriever":
        """Build the memory retriever from configuration settings."""
        retriever = get_retriever(
            embedding_model_id=settings.RAG_TEXT_EMBEDDING_MODEL_ID,
            mongo_uri=settings.MONGO_URI,
            db_name=settings.MONGO_DB_NAME,
            collection_name=settings.MONGO_RESUME_LONG_TERM_MEMORY_COLLECTION,
            k=settings.RAG_TOP_K,
            device=settings.RAG_DEVICE,
        )

        return cls(retriever)

    def retrieve(self, query: str, user_id: str = None) -> list[Document]:
        """Retrieve relevant documents from long-term memory.
        
        Args:
            query: Search query
            user_id: Optional user ID for user-specific retrieval
            
        Returns:
            List of relevant documents
        """
        # For now, we don't filter by user_id, but this can be added later
        # when we implement user-specific memory
        return self.retriever.invoke(query)
