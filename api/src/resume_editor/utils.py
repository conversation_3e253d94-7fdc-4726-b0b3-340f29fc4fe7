"""Utility functions for the resume editor."""

import json
import os
from datetime import date
from pathlib import Path
from typing import Dict, Optional, Any

from loguru import logger

from .domain.entities import Resume, Contact


def initialize_resume(
    user_id: str,
    name: str,
    email: str,
    title: Optional[str] = None,
    data_path: str = "./data",
) -> Dict[str, Any]:
    """Initialize a new resume for a user.

    Args:
        user_id: User identifier
        name: User's full name
        email: User's email address
        title: User's professional title (optional)
        data_path: Path to store resume data

    Returns:
        Dictionary with status and message
    """
    # Create storage directory if it doesn't exist
    storage_path = Path(data_path)
    storage_path.mkdir(exist_ok=True, parents=True)

    # Check if resume already exists
    resume_path = storage_path / f"{user_id}.json"
    if resume_path.exists():
        return {
            "status": "error",
            "message": f"Resume already exists for user {user_id}"
        }

    # Create basic contact info
    contact = Contact(email=email)

    # Create minimal resume
    resume = Resume(
        name=name,
        title=title,
        contact=contact,
    )

    # Save resume
    try:
        with open(resume_path, "w") as f:
            # Handle date serialization
            def serialize_date(obj):
                if isinstance(obj, date):
                    return obj.isoformat()
                raise TypeError(f"Type {type(obj)} not serializable")

            json.dump(resume.model_dump(), f, default=serialize_date, indent=2)

        return {
            "status": "success",
            "message": f"Resume initialized for user {user_id}",
            "data": resume.model_dump(),
        }
    except Exception as e:
        logger.error(f"Error initializing resume for user {user_id}: {e}")
        return {"status": "error", "message": str(e)}


def create_minimal_resume(
    name: str,
    email: str,
    title: Optional[str] = None
) -> Resume:
    """Create a minimal resume with just the name and contact info.

    Args:
        name: User's full name
        email: User's email address
        title: User's professional title (optional)

    Returns:
        Minimal Resume object
    """
    # Create basic contact info
    contact = Contact(email=email)

    # Create minimal resume
    resume = Resume(
        name=name,
        title=title,
        contact=contact
    )

    return resume


def load_sample_resume(sample_path: str = "./data/sample_resume.json") -> Optional[Resume]:
    """Load the sample resume.

    Args:
        sample_path: Path to sample resume

    Returns:
        Sample Resume object or None if not found
    """
    # Check if sample exists
    sample_file = Path(sample_path)
    if not sample_file.exists():
        logger.warning(f"Sample resume not found at {sample_path}")
        return None

    try:
        with open(sample_file, "r") as src:
            resume_data = json.load(src)

        # Convert dates from strings to date objects if needed
        if "education" in resume_data:
            for edu in resume_data["education"]:
                if "start_date" in edu and isinstance(edu["start_date"], str):
                    edu["start_date"] = date.fromisoformat(edu["start_date"])
                if "end_date" in edu and edu["end_date"] and isinstance(edu["end_date"], str):
                    edu["end_date"] = date.fromisoformat(edu["end_date"])

        if "experience" in resume_data:
            for exp in resume_data["experience"]:
                if "start_date" in exp and isinstance(exp["start_date"], str):
                    exp["start_date"] = date.fromisoformat(exp["start_date"])
                if "end_date" in exp and exp["end_date"] and isinstance(exp["end_date"], str):
                    exp["end_date"] = date.fromisoformat(exp["end_date"])

        if "projects" in resume_data:
            for proj in resume_data["projects"]:
                if "start_date" in proj and proj["start_date"] and isinstance(proj["start_date"], str):
                    proj["start_date"] = date.fromisoformat(proj["start_date"])
                if "end_date" in proj and proj["end_date"] and isinstance(proj["end_date"], str):
                    proj["end_date"] = date.fromisoformat(proj["end_date"])

        # Create Resume object
        return Resume(**resume_data)
    except Exception as e:
        logger.error(f"Error loading sample resume: {e}")
        return None


def copy_sample_resume(
    user_id: str,
    sample_path: str = "./data/sample_resume.json",
    data_path: str = "./data"
) -> Dict[str, Any]:
    """Copy the sample resume for a user.

    Args:
        user_id: User identifier
        sample_path: Path to sample resume
        data_path: Path to store resume data

    Returns:
        Dictionary with status and message
    """
    # Create storage directory if it doesn't exist
    storage_path = Path(data_path)
    storage_path.mkdir(exist_ok=True, parents=True)

    # Check if resume already exists
    resume_path = storage_path / f"{user_id}.json"
    if resume_path.exists():
        return {
            "status": "error",
            "message": f"Resume already exists for user {user_id}"
        }

    # Check if sample exists
    sample_file = Path(sample_path)
    if not sample_file.exists():
        return {
            "status": "error",
            "message": f"Sample resume not found at {sample_path}"
        }

    # Copy sample
    try:
        with open(sample_file, "r") as src:
            resume_data = json.load(src)

        with open(resume_path, "w") as dst:
            json.dump(resume_data, dst, indent=2)

        return {
            "status": "success",
            "message": f"Sample resume copied for user {user_id}"
        }
    except Exception as e:
        logger.error(f"Error copying sample resume for user {user_id}: {e}")
        return {"status": "error", "message": str(e)}


def load_config(config_path: str = "./config.json") -> Dict[str, Any]:
    """Load configuration from a JSON file.

    Args:
        config_path: Path to configuration file

    Returns:
        Configuration dictionary
    """
    try:
        with open(config_path, "r") as f:
            config = json.load(f)
        return config
    except Exception as e:
        logger.error(f"Error loading configuration: {e}")
        return {}
