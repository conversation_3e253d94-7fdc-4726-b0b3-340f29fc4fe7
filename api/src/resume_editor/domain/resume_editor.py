from typing import List
from pydantic import BaseModel, Field


class ResumeEditor(BaseModel):
    """A class representing a resume editor agent with specialized expertise.

    Args:
        id (str): Unique identifier for the resume editor.
        name (str): Name of the resume editor.
        specialty (str): The editor's area of expertise and specialization.
        approach (str): Description of the editor's methodology and editing style.
        focus_areas (List[str]): Key areas of focus and expertise.
    """

    id: str = Field(description="Unique identifier for the resume editor")
    name: str = <PERSON>(description="Name of the resume editor")
    specialty: str = Field(description="The editor's area of expertise and specialization")
    approach: str = Field(description="Description of the editor's methodology and editing style")
    focus_areas: List[str] = Field(description="Key areas of focus and expertise")

    def __str__(self) -> str:
        return f"ResumeEditor(id={self.id}, name={self.name}, specialty={self.specialty}, approach={self.approach}, focus_areas={self.focus_areas})"
