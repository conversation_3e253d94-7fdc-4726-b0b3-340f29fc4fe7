"""Test script for the resume copilot agent."""

import argparse
import async<PERSON>
import json
import os
from pathlib import Path

from loguru import logger

from .agent import AgentConfig, create_agent, ChatSession
from .utils import copy_sample_resume, load_config


async def interactive_chat(user_id: str, config_path: str = "./config.json"):
    """Start an interactive chat session with the resume copilot agent.
    
    Args:
        user_id: User identifier
        config_path: Path to configuration file
    """
    print(f"\n📝 Resume Copilot Chat (User ID: {user_id})")
    print("Type 'exit' or 'quit' to end the session.\n")
    
    # Load configuration
    config_dict = load_config(config_path)
    if not config_dict:
        print("❌ Error: Failed to load configuration. Please make sure config.json exists.")
        return
    
    # Set environment variables
    for key, value in config_dict.items():
        os.environ[key] = str(value)
    
    try:
        # Create agent config
        agent_config = AgentConfig()
        
        # Create agent
        agent_app, checkpoint_store = create_agent(agent_config)
        
        # Check if resume exists
        resume_path = Path(agent_config.resume_data_path) / f"{user_id}.json"
        if not resume_path.exists():
            print("⚠️ No resume found for this user ID.")
            create_sample = input("Would you like to create a sample resume for testing? (y/n): ")
            if create_sample.lower() == 'y':
                result = copy_sample_resume(
                    user_id=user_id,
                    data_path=agent_config.resume_data_path
                )
                print(f"📋 {result['message']}")
            else:
                print("❌ A resume is required to use the copilot. Exiting.")
                return
        
        # Create chat session
        session = ChatSession(
            user_id=user_id,
            config=agent_config,
            agent_app=agent_app,
            checkpoint_store=checkpoint_store
        )
        
        # Initial greeting
        greeting = await session.send_message(
            "Hello, I'd like some help with my resume."
        )
        print(f"🤖 Agent: {greeting}\n")
        
        # Interactive loop
        while True:
            user_input = input("👤 You: ")
            if user_input.lower() in ['exit', 'quit', 'bye', 'goodbye']:
                print("🤖 Agent: Goodbye! Good luck with your resume!")
                break
            
            print("\n🔄 Processing...")
            response = await session.send_message(user_input)
            print(f"🤖 Agent: {response}\n")
    
    except Exception as e:
        logger.error(f"Error in interactive chat: {e}")
        print(f"❌ An error occurred: {str(e)}")


def main():
    """Main entry point for testing the agent."""
    parser = argparse.ArgumentParser(description="Test the Resume Copilot Agent")
    parser.add_argument(
        "--user-id", 
        type=str, 
        default="test_user",
        help="User ID for testing"
    )
    parser.add_argument(
        "--config", 
        type=str, 
        default="./config.json",
        help="Path to configuration file"
    )
    
    args = parser.parse_args()
    
    # Run interactive chat
    asyncio.run(interactive_chat(args.user_id, args.config))


if __name__ == "__main__":
    main()
