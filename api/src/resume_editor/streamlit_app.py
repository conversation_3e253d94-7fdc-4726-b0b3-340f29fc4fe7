"""Streamlit UI for testing the resume copilot agent."""

import asyncio
import json
import os
import sys
from datetime import date, datetime
from pathlib import Path
from typing import Dict, Any, Optional, List

import httpx
import streamlit as st
from loguru import logger

from resume_editor.agent import AgentConfig, create_agent, ChatSession
from resume_editor.models import Resume, Contact, Education, Experience, Skill, Project
from resume_editor.tools import ResumeStore
from resume_editor.utils import initialize_resume, copy_sample_resume, load_config


# Configure page
st.set_page_config(
    page_title="Resume Copilot",
    page_icon="📝",
    layout="wide",
    initial_sidebar_state="expanded",
)


# Helper functions
def date_serializer(obj):
    """Helper to serialize date objects to JSON."""
    if isinstance(obj, (date, datetime)):
        return obj.isoformat()
    raise TypeError(f"Type {type(obj)} not serializable")


def format_date(date_str: Optional[str]) -> str:
    """Format date for display."""
    if not date_str:
        return "Present"
    try:
        dt = date.fromisoformat(date_str)
        return dt.strftime("%b %Y")
    except:
        return date_str


def load_resume(user_id: str, data_path: str = "./data") -> Optional[Resume]:
    """Load resume for a user."""
    resume_store = ResumeStore(data_path)
    return resume_store.load_resume(user_id)


def save_resume(user_id: str, resume: Resume, data_path: str = "./data") -> bool:
    """Save resume for a user."""
    resume_store = ResumeStore(data_path)
    return resume_store.save_resume(user_id, resume)


def display_resume(resume: Resume):
    """Display resume in a formatted way."""
    st.title(resume.name)
    if resume.title:
        st.subheader(resume.title)
    
    # Contact info
    contact_cols = st.columns(3)
    with contact_cols[0]:
        st.write(f"📧 {resume.contact.email}")
    if resume.contact.phone:
        with contact_cols[1]:
            st.write(f"📱 {resume.contact.phone}")
    if resume.contact.linkedin:
        with contact_cols[2]:
            st.write(f"🔗 LinkedIn: {resume.contact.linkedin}")
    
    # Summary
    if resume.summary:
        st.markdown("### Summary")
        st.write(resume.summary)
    
    # Experience
    if resume.experience:
        st.markdown("### Experience")
        for exp in resume.experience:
            exp_col1, exp_col2 = st.columns([3, 1])
            with exp_col1:
                st.markdown(f"**{exp.position}** at *{exp.company}*")
            with exp_col2:
                end_date = format_date(exp.end_date.isoformat() if exp.end_date else None)
                start_date = format_date(exp.start_date.isoformat())
                st.markdown(f"*{start_date} - {end_date}*")
            
            if exp.description:
                st.write(exp.description)
            
            if exp.highlights:
                for highlight in exp.highlights:
                    st.markdown(f"- {highlight}")
    
    # Education
    if resume.education:
        st.markdown("### Education")
        for edu in resume.education:
            edu_col1, edu_col2 = st.columns([3, 1])
            with edu_col1:
                st.markdown(f"**{edu.degree}** in *{edu.field_of_study}*")
                st.markdown(f"*{edu.institution}*")
            with edu_col2:
                end_date = format_date(edu.end_date.isoformat() if edu.end_date else None)
                start_date = format_date(edu.start_date.isoformat())
                st.markdown(f"*{start_date} - {end_date}*")
            
            if edu.gpa:
                st.markdown(f"GPA: {edu.gpa}")
            
            if edu.description:
                st.write(edu.description)
    
    # Skills
    if resume.skills:
        st.markdown("### Skills")
        skill_cols = st.columns(3)
        skill_items = []
        
        for i, skill in enumerate(resume.skills):
            skill_text = f"**{skill.name}**"
            if skill.level:
                skill_text += f" - *{skill.level}*"
            skill_items.append(skill_text)
        
        # Distribute skills among columns
        skills_per_col = len(skill_items) // 3 + (1 if len(skill_items) % 3 > 0 else 0)
        for i, col in enumerate(skill_cols):
            start_idx = i * skills_per_col
            end_idx = min(start_idx + skills_per_col, len(skill_items))
            col_skills = skill_items[start_idx:end_idx]
            if col_skills:
                for skill in col_skills:
                    col.markdown(f"- {skill}")
    
    # Projects
    if resume.projects:
        st.markdown("### Projects")
        for project in resume.projects:
            proj_col1, proj_col2 = st.columns([3, 1])
            with proj_col1:
                project_title = f"**{project.name}**"
                if project.url:
                    project_title += f" [🔗]({project.url})"
                st.markdown(project_title)
            
            with proj_col2:
                if project.start_date or project.end_date:
                    start = format_date(project.start_date.isoformat() if project.start_date else None)
                    end = format_date(project.end_date.isoformat() if project.end_date else None)
                    if start and end:
                        st.markdown(f"*{start} - {end}*")
            
            st.write(project.description)
            
            if project.highlights:
                for highlight in project.highlights:
                    st.markdown(f"- {highlight}")
    
    # Languages
    if resume.languages:
        st.markdown("### Languages")
        st.write(", ".join(resume.languages))
    
    # Certifications
    if resume.certifications:
        st.markdown("### Certifications")
        for cert in resume.certifications:
            st.markdown(f"- {cert}")
    
    # Custom sections
    if resume.custom_sections:
        for title, content in resume.custom_sections.items():
            st.markdown(f"### {title}")
            for item in content:
                st.markdown(f"- {item}")


async def chat_with_agent(
    user_id: str, 
    message: str, 
    agent_config: AgentConfig,
    agent_app,
    checkpoint_store,
) -> str:
    """Chat with the resume copilot agent."""
    try:
        # Get or create chat session
        if "chat_session" not in st.session_state:
            st.session_state.chat_session = ChatSession(
                user_id=user_id,
                config=agent_config,
                agent_app=agent_app,
                checkpoint_store=checkpoint_store
            )
        
        # Send message to agent
        response = await st.session_state.chat_session.send_message(message)
        return response
    except Exception as e:
        logger.error(f"Error in chat: {e}")
        return f"Error: {str(e)}"


# Main function for the Streamlit app
def main():
    """Main function for the Streamlit app."""
    st.sidebar.title("Resume Copilot")
    st.sidebar.markdown("A smart assistant for improving your resume")
    
    # User ID input
    if "user_id" not in st.session_state:
        st.session_state.user_id = "test_user"
    
    user_id = st.sidebar.text_input("User ID", value=st.session_state.user_id)
    if user_id != st.session_state.user_id:
        st.session_state.user_id = user_id
        # Reset chat session when user changes
        if "chat_session" in st.session_state:
            del st.session_state.chat_session
    
    # Configuration section
    with st.sidebar.expander("Configuration"):
        config_path = st.text_input(
            "Config path", 
            value=os.getenv("RESUME_CONFIG_PATH", "./config.json")
        )
        data_path = st.text_input(
            "Data path", 
            value=os.getenv("RESUME_DATA_PATH", "./data")
        )
    
    # Resume management
    with st.sidebar.expander("Resume Management"):
        if st.button("Initialize New Resume"):
            with st.form("init_resume_form"):
                name = st.text_input("Full Name")
                email = st.text_input("Email")
                title = st.text_input("Professional Title")
                submit_button = st.form_submit_button("Create Resume")
                
                if submit_button:
                    if not name or not email:
                        st.error("Name and email are required")
                    else:
                        result = initialize_resume(
                            user_id=user_id,
                            name=name,
                            email=email,
                            title=title,
                            data_path=data_path
                        )
                        if result["status"] == "success":
                            st.success(f"Resume initialized for {name}")
                            st.rerun()
                        else:
                            st.error(result["message"])
        
        if st.button("Use Sample Resume"):
            result = copy_sample_resume(
                user_id=user_id,
                data_path=data_path
            )
            if result["status"] == "success":
                st.sidebar.success("Sample resume copied successfully")
                st.rerun()
            else:
                st.sidebar.error(result["message"])
    
    # Check if resume exists
    resume = load_resume(user_id, data_path)
    if not resume:
        st.info(
            "No resume found for this user ID. "
            "Please initialize a new resume or use the sample resume."
        )
        return
    
    # Main content - Tabs
    tab1, tab2 = st.tabs(["Resume", "Copilot Chat"])
    
    # Tab 1: Resume Display
    with tab1:
        display_resume(resume)
        
        # Edit raw JSON
        with st.expander("Edit Raw Resume Data"):
            resume_json = json.dumps(resume.model_dump(), indent=2, default=date_serializer)
            edited_json = st.text_area(
                "Edit JSON", 
                value=resume_json,
                height=500
            )
            
            if st.button("Save Changes"):
                try:
                    # Parse JSON and create Resume object
                    edited_data = json.loads(edited_json)
                    
                    # Convert string dates to date objects
                    if "education" in edited_data:
                        for edu in edited_data["education"]:
                            if "start_date" in edu and edu["start_date"]:
                                edu["start_date"] = date.fromisoformat(edu["start_date"])
                            if "end_date" in edu and edu["end_date"]:
                                edu["end_date"] = date.fromisoformat(edu["end_date"])
                    
                    if "experience" in edited_data:
                        for exp in edited_data["experience"]:
                            if "start_date" in exp and exp["start_date"]:
                                exp["start_date"] = date.fromisoformat(exp["start_date"])
                            if "end_date" in exp and exp["end_date"]:
                                exp["end_date"] = date.fromisoformat(exp["end_date"])
                    
                    if "projects" in edited_data:
                        for proj in edited_data["projects"]:
                            if "start_date" in proj and proj["start_date"]:
                                proj["start_date"] = date.fromisoformat(proj["start_date"])
                            if "end_date" in proj and proj["end_date"]:
                                proj["end_date"] = date.fromisoformat(proj["end_date"])
                    
                    # Create and save resume
                    updated_resume = Resume(**edited_data)
                    success = save_resume(user_id, updated_resume, data_path)
                    
                    if success:
                        st.success("Resume updated successfully")
                        st.rerun()
                    else:
                        st.error("Failed to save resume")
                
                except Exception as e:
                    st.error(f"Error updating resume: {str(e)}")
    
    # Tab 2: Chat with Copilot
    with tab2:
        # Load configuration for the agent
        try:
            # Load config
            config_dict = load_config(config_path)
            if not config_dict:
                st.error("Failed to load configuration from config.json")
                return
            
            # Set environment variables
            for key, value in config_dict.items():
                os.environ[key] = str(value)
            
            # Create agent config
            agent_config = AgentConfig()
            
            # Create agent
            agent_app, checkpoint_store = create_agent(agent_config)
            
            # Initialize chat history
            if "messages" not in st.session_state:
                st.session_state.messages = [
                    {"role": "assistant", "content": f"Hi there! I'm your Resume Copilot. I can help you improve your resume. What would you like help with today?"}
                ]
            
            # Display chat messages
            for message in st.session_state.messages:
                with st.chat_message(message["role"]):
                    st.write(message["content"])
            
            # User input
            if prompt := st.chat_input("Chat with your Resume Copilot"):
                # Add user message to chat history
                st.session_state.messages.append({"role": "user", "content": prompt})
                
                # Display user message
                with st.chat_message("user"):
                    st.write(prompt)
                
                # Get agent response
                with st.chat_message("assistant"):
                    message_placeholder = st.empty()
                    message_placeholder.write("⌛ Thinking...")
                    
                    # Use asyncio to chat with agent
                    response = asyncio.run(
                        chat_with_agent(
                            user_id=user_id,
                            message=prompt,
                            agent_config=agent_config,
                            agent_app=agent_app,
                            checkpoint_store=checkpoint_store
                        )
                    )
                    
                    # Update placeholder with response
                    message_placeholder.write(response)
                
                # Add assistant response to chat history
                st.session_state.messages.append({"role": "assistant", "content": response})
                
                # Reload resume to show any changes
                new_resume = load_resume(user_id, data_path)
                if new_resume and new_resume.model_dump() != resume.model_dump():
                    st.rerun()
        
        except Exception as e:
            st.error(f"Error initializing the agent: {str(e)}")
            st.info("Please check your configuration in config.json")


if __name__ == "__main__":
    main()
