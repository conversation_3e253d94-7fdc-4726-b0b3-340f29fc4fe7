from pathlib import Path
from pydantic import Field

from common.config.base_settings import BaseAgentSettings


class ResumeEditorSettings(BaseAgentSettings):
    """Settings specific to resume editor agents."""

    # --- MongoDB Collections for Resume Editor ---
    MONGO_RESUME_STATE_CHECKPOINT_COLLECTION: str = "resume_editor_state_checkpoints"
    MONGO_RESUME_STATE_WRITES_COLLECTION: str = "resume_editor_state_writes"
    MONGO_RESUME_LONG_TERM_MEMORY_COLLECTION: str = "resume_editor_long_term_memory"

    # --- Resume Editor Specific Configuration ---
    RESUME_EDITOR_PROJECT: str = Field(
        default="resume_editor",
        description="Project name for resume editor tracking.",
    )

    # --- Resume Data Storage ---
    RESUME_DATA_PATH: str = Field(
        default="./data/resumes",
        description="Path to store resume data files.",
    )

    # --- LLM Configuration ---
    GROQ_API_KEY: str = Field(
        default="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",
        description="Groq API key for LLM access.",
    )
    GROQ_LLM_MODEL: str = Field(
        default="llama-3.3-70b-versatile",
        description="Groq LLM model to use.",
    )
    LLM_TEMPERATURE: float = Field(
        default=0.3,
        description="Temperature setting for LLM responses.",
    )

    # --- Evaluation Dataset Path ---
    RESUME_EVALUATION_DATASET_FILE_PATH: Path = Path("data/resume_evaluation_dataset.json")
    RESUME_EXTRACTION_METADATA_FILE_PATH: Path = Path("data/resume_extraction_metadata.json")


settings = ResumeEditorSettings()
