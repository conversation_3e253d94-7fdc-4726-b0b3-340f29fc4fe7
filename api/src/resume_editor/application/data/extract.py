"""Data extraction services for resume editor."""

import json
from pathlib import Path
from typing import Dict, List, Any, Optional

from loguru import logger

from resume_editor.domain.entities import Resume
from resume_editor.tools import ResumeStore, _serialize_date
from resume_editor.config import settings


class ResumeDataExtractor:
    """Service for extracting and processing resume data."""
    
    def __init__(self, data_path: str = None):
        """Initialize the data extractor.
        
        Args:
            data_path: Path to resume data storage
        """
        self.data_path = data_path or settings.RESUME_DATA_PATH
        self.resume_store = ResumeStore(self.data_path)
    
    def extract_user_resume(self, user_id: str) -> Optional[Dict[str, Any]]:
        """Extract resume data for a specific user.
        
        Args:
            user_id: User identifier
            
        Returns:
            Resume data dictionary or None if not found
        """
        try:
            resume_data = self.resume_store.get_resume(user_id)
            if "error" not in resume_data:
                return resume_data
            return None
        except Exception as e:
            logger.error(f"Error extracting resume for user {user_id}: {e}")
            return None
    
    def extract_all_resumes(self) -> List[Dict[str, Any]]:
        """Extract all resume data from storage.
        
        Returns:
            List of resume data dictionaries
        """
        resumes = []
        data_path = Path(self.data_path)
        
        if not data_path.exists():
            logger.warning(f"Resume data path does not exist: {data_path}")
            return resumes
        
        try:
            for resume_file in data_path.glob("*.json"):
                if resume_file.name == "sample_resume.json":
                    continue  # Skip sample resume
                
                try:
                    with open(resume_file, 'r') as f:
                        resume_data = json.load(f)
                        resume_data['user_id'] = resume_file.stem
                        resumes.append(resume_data)
                except Exception as e:
                    logger.error(f"Error reading resume file {resume_file}: {e}")
                    continue
        
        except Exception as e:
            logger.error(f"Error extracting all resumes: {e}")
        
        return resumes
    
    def extract_skills_summary(self) -> Dict[str, int]:
        """Extract a summary of all skills across resumes.
        
        Returns:
            Dictionary mapping skill names to frequency counts
        """
        skills_count = {}
        resumes = self.extract_all_resumes()
        
        for resume in resumes:
            skills = resume.get('skills', [])
            for skill in skills:
                skill_name = skill.get('name', '') if isinstance(skill, dict) else str(skill)
                if skill_name:
                    skills_count[skill_name] = skills_count.get(skill_name, 0) + 1
        
        return dict(sorted(skills_count.items(), key=lambda x: x[1], reverse=True))
    
    def extract_experience_summary(self) -> Dict[str, Any]:
        """Extract summary statistics about work experience.
        
        Returns:
            Dictionary with experience statistics
        """
        companies = set()
        positions = set()
        total_experiences = 0
        resumes = self.extract_all_resumes()
        
        for resume in resumes:
            experiences = resume.get('experience', [])
            total_experiences += len(experiences)
            
            for exp in experiences:
                if isinstance(exp, dict):
                    company = exp.get('company', '')
                    position = exp.get('position', '')
                    if company:
                        companies.add(company)
                    if position:
                        positions.add(position)
        
        return {
            'total_experiences': total_experiences,
            'unique_companies': len(companies),
            'unique_positions': len(positions),
            'top_companies': list(companies)[:10],
            'top_positions': list(positions)[:10],
        }
    
    def extract_education_summary(self) -> Dict[str, Any]:
        """Extract summary statistics about education.
        
        Returns:
            Dictionary with education statistics
        """
        institutions = set()
        degrees = set()
        fields = set()
        total_education = 0
        resumes = self.extract_all_resumes()
        
        for resume in resumes:
            education = resume.get('education', [])
            total_education += len(education)
            
            for edu in education:
                if isinstance(edu, dict):
                    institution = edu.get('institution', '')
                    degree = edu.get('degree', '')
                    field = edu.get('field_of_study', '')
                    
                    if institution:
                        institutions.add(institution)
                    if degree:
                        degrees.add(degree)
                    if field:
                        fields.add(field)
        
        return {
            'total_education_entries': total_education,
            'unique_institutions': len(institutions),
            'unique_degrees': len(degrees),
            'unique_fields': len(fields),
            'top_institutions': list(institutions)[:10],
            'top_degrees': list(degrees)[:10],
            'top_fields': list(fields)[:10],
        }
    
    def extract_resume_metadata(self, user_id: str) -> Dict[str, Any]:
        """Extract metadata about a specific resume.
        
        Args:
            user_id: User identifier
            
        Returns:
            Dictionary with resume metadata
        """
        resume_data = self.extract_user_resume(user_id)
        if not resume_data:
            return {}
        
        metadata = {
            'user_id': user_id,
            'has_summary': bool(resume_data.get('summary')),
            'skills_count': len(resume_data.get('skills', [])),
            'experience_count': len(resume_data.get('experience', [])),
            'education_count': len(resume_data.get('education', [])),
            'projects_count': len(resume_data.get('projects', [])),
            'languages_count': len(resume_data.get('languages', [])),
            'certifications_count': len(resume_data.get('certifications', [])),
            'custom_sections_count': len(resume_data.get('custom_sections', {})),
        }
        
        # Calculate completeness score
        total_sections = 8  # summary, skills, experience, education, projects, languages, certifications, custom
        filled_sections = sum([
            metadata['has_summary'],
            metadata['skills_count'] > 0,
            metadata['experience_count'] > 0,
            metadata['education_count'] > 0,
            metadata['projects_count'] > 0,
            metadata['languages_count'] > 0,
            metadata['certifications_count'] > 0,
            metadata['custom_sections_count'] > 0,
        ])
        
        metadata['completeness_score'] = round((filled_sections / total_sections) * 100, 2)
        
        return metadata
