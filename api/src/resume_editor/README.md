# Resume Editor - Clean Architecture

## 🎯 Overview
The resume editor has been restructured to follow the same clean architecture pattern as the career coaches, with all unnecessary files removed and integration into the main API.

## 📁 Final Structure

```
api/src/resume_editor/
├── __init__.py                    # Module exports
├── config.py                      # Configuration settings
├── tools.py                       # Resume tools (kept for functionality)
├── utils.py                       # Utility functions (kept for functionality)
│
├── domain/                        # Domain layer
│   ├── __init__.py
│   ├── entities.py               # Resume domain entities
│   ├── resume_editor.py          # Resume editor domain model
│   ├── resume_editor_factory.py  # Factory pattern
│   ├── prompts.py               # Centralized prompts
│   └── evaluation.py            # Evaluation domain models
│
├── application/                   # Application layer
│   ├── __init__.py
│   ├── long_term_memory.py       # Memory management
│   ├── conversation_service/     # Conversation management
│   │   ├── __init__.py
│   │   ├── generate_response.py  # Response generation with opik
│   │   ├── reset_conversation.py # Conversation reset
│   │   └── workflow/            # LangGraph workflow
│   │       ├── __init__.py
│   │       ├── state.py         # State management
│   │       ├── chains.py        # LLM chains
│   │       ├── nodes.py         # Workflow nodes
│   │       ├── edges.py         # Workflow edges
│   │       └── graph.py         # Main workflow graph
│   ├── data/                    # Data services
│   │   ├── __init__.py
│   │   └── extract.py          # Data extraction
│   └── evaluation/              # Evaluation services
│       └── __init__.py
│
└── infrastructure/               # Infrastructure layer
    ├── __init__.py
    └── api.py                   # FastAPI with opik integration
```

## 🗑️ Removed Files
- ❌ `agent.py` - Old agent implementation
- ❌ `api.py` - Old API (replaced by infrastructure/api.py)
- ❌ `main.py` - Standalone main (integrated into src/main.py)
- ❌ `models.py` - Old models (moved to domain/entities.py)
- ❌ `streamlit_app.py` - Streamlit components (removed as requested)
- ❌ `test_agent.py` - Old testing approach

## 🔗 Integration with Main API

The resume editor is now integrated into the main API at `api/src/main.py`:

```python
# Mounted at /resume-editor
app.mount("/resume-editor", resume_editor_app)
```

### Available Endpoints:
- `GET /resume-editor/health` - Health check
- `GET /resume-editor/editors` - List available editors
- `POST /resume-editor/chat` - Chat with resume editor
- `WebSocket /resume-editor/chat/stream` - Streaming chat
- `POST /resume-editor/reset` - Reset conversation
- `POST /resume-editor/resume/init` - Initialize new resume
- `POST /resume-editor/resume/copy-sample` - Copy sample resume
- `GET /resume-editor/resume/{user_id}` - Get user resume

## 🚀 Usage

### Starting the Platform:
```bash
cd api/src
python main.py
```

The resume editor will be available at: `http://localhost:8000/resume-editor/`

### API Documentation:
- Main platform: `http://localhost:8000/docs`
- Resume editor: `http://localhost:8000/resume-editor/docs`

## ✨ Key Features

### 🏗️ Clean Architecture
- **Domain Layer**: Business logic and entities
- **Application Layer**: Use cases and workflows
- **Infrastructure Layer**: External concerns (API, database)

### 🧠 Memory & Monitoring
- Long-term memory for context retention
- Opik integration for conversation tracking
- MongoDB state management
- User-specific conversation threads

### 🔄 Workflow
- LangGraph-based conversation workflow
- Intent classification (edit vs chat)
- Resume editing with tool integration
- Conversation summarization

### 🎯 Resume Editor Types
- **resume_builder**: General resume creation and editing
- **ats_optimizer**: ATS optimization and keyword enhancement
- **career_advisor**: Career strategy and positioning
- **content_enhancer**: Content enhancement and impact statements

## 🔧 Configuration

Settings are managed in `config.py`:
- MongoDB collections for state management
- Opik monitoring configuration
- LLM settings (Groq integration)
- Resume data storage paths

## 📊 Monitoring

All conversations are tracked with Opik for:
- Performance monitoring
- Conversation analysis
- Error tracking
- Usage analytics

## 🎉 Benefits

1. **Consistency**: Follows same pattern as career_coaches
2. **Maintainability**: Clean separation of concerns
3. **Scalability**: Easy to add new editor types
4. **Integration**: Seamlessly integrated into main platform
5. **Monitoring**: Comprehensive tracking and analytics
6. **Memory**: Context-aware conversations
7. **Clean**: Removed all unnecessary files

The resume editor is now a clean, well-structured module that integrates seamlessly with the main AI Agents Platform.
