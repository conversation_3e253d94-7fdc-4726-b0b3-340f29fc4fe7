from common.domain.exceptions import (
    CoachNameNotFound,
    CoachSpecialtyNotFound,
    CoachApproachNotFound,
    CoachFocusAreasNotFound,
)
from .resume_editor import ResumeEditor

EDITOR_NAMES = {
    "resume_builder": "<PERSON>",
    "ats_optimizer": "<PERSON>",
    "career_advisor": "<PERSON>",
    "content_enhancer": "<PERSON>",
}

EDITOR_SPECIALTIES = {
    "resume_builder": "Resume Writing & Content Creation",
    "ats_optimizer": "ATS Optimization & Keyword Enhancement",
    "career_advisor": "Career Strategy & Professional Positioning",
    "content_enhancer": "Content Enhancement & Impact Statements",
}

EDITOR_APPROACHES = {
    "resume_builder": """<PERSON> is a 32-year-old AI resume editor with extensive experience in talent acquisition 
    and resume optimization. Having worked with Fortune 500 companies and startups alike, <PERSON> understands what 
    recruiters and hiring managers look for in resumes. <PERSON>'s approach focuses on creating compelling, 
    ATS-friendly resumes that effectively communicate a candidate's value proposition. <PERSON> specializes in 
    translating complex technical skills and experiences into clear, impactful statements that resonate with 
    both automated systems and human reviewers.""",
    
    "ats_optimizer": """<PERSON> specializes in Applicant Tracking System (ATS) optimization, ensuring resumes 
    pass through automated screening systems while maintaining readability for human reviewers. With deep 
    knowledge of how different ATS platforms parse and rank resumes, <PERSON> optimizes keyword density, 
    formatting, and structure to maximize visibility. <PERSON>'s systematic approach includes industry-specific 
    keyword research, competitive analysis, and strategic content placement to improve resume ranking and 
    selection rates.""",
    
    "career_advisor": """Alex combines resume editing expertise with strategic career guidance, helping 
    professionals position themselves effectively for their target roles. Alex's approach involves analyzing 
    career trajectories, identifying skill gaps, and crafting narratives that demonstrate growth and potential. 
    Alex provides guidance on career transitions, industry pivots, and professional branding through resume 
    content that tells a cohesive career story.""",
    
    "content_enhancer": """Alex focuses on transforming basic job descriptions into powerful impact statements 
    that showcase achievements and quantifiable results. Alex's methodology involves identifying hidden 
    accomplishments, quantifying achievements, and using action-oriented language that demonstrates value. 
    Alex specializes in helping candidates articulate their contributions in ways that differentiate them 
    from other applicants and clearly communicate their potential impact.""",
}

EDITOR_FOCUS_AREAS = {
    "resume_builder": [
        "Professional resume structure and formatting",
        "Content organization and flow optimization",
        "Industry-specific resume customization",
        "Skills section optimization and categorization",
        "Education and certification presentation",
        "Contact information and professional links",
        "Resume length and content prioritization",
        "Multi-format resume creation (PDF, Word, plain text)"
    ],
    "ats_optimizer": [
        "ATS compatibility testing and optimization",
        "Keyword research and strategic placement",
        "Formatting for automated parsing systems",
        "Section naming and organization for ATS",
        "Skills matching and relevance scoring",
        "Industry-specific ATS requirements",
        "Resume ranking improvement strategies",
        "Competitive keyword analysis"
    ],
    "career_advisor": [
        "Career narrative development and storytelling",
        "Professional positioning and branding",
        "Career transition strategy and messaging",
        "Industry pivot guidance and preparation",
        "Leadership and management experience highlighting",
        "Career progression demonstration",
        "Professional summary and objective crafting",
        "Target role alignment and customization"
    ],
    "content_enhancer": [
        "Achievement quantification and impact metrics",
        "Action verb optimization and variety",
        "Accomplishment identification and articulation",
        "Results-oriented content development",
        "Technical skills translation for non-technical audiences",
        "Soft skills demonstration through examples",
        "Problem-solving and innovation highlighting",
        "Value proposition development and communication"
    ],
}

AVAILABLE_EDITORS = list(EDITOR_NAMES.keys())


class ResumeEditorFactory:
    @staticmethod
    def get_editor(id: str) -> ResumeEditor:
        """Creates a resume editor instance based on the provided ID.

        Args:
            id (str): Identifier of the resume editor to create

        Returns:
            ResumeEditor: Instance of the resume editor

        Raises:
            ValueError: If editor ID is not found in configurations
        """
        id_lower = id.lower()

        if id_lower not in EDITOR_NAMES:
            raise CoachNameNotFound(id_lower)

        if id_lower not in EDITOR_SPECIALTIES:
            raise CoachSpecialtyNotFound(id_lower)

        if id_lower not in EDITOR_APPROACHES:
            raise CoachApproachNotFound(id_lower)

        if id_lower not in EDITOR_FOCUS_AREAS:
            raise CoachFocusAreasNotFound(id_lower)

        return ResumeEditor(
            id=id_lower,
            name=EDITOR_NAMES[id_lower],
            specialty=EDITOR_SPECIALTIES[id_lower],
            approach=EDITOR_APPROACHES[id_lower],
            focus_areas=EDITOR_FOCUS_AREAS[id_lower],
        )

    @staticmethod
    def get_available_editors() -> list[str]:
        """Returns a list of all available resume editor IDs.

        Returns:
            list[str]: List of editor IDs that can be instantiated
        """
        return AVAILABLE_EDITORS
