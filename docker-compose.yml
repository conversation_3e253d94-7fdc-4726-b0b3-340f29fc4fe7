services:
  api:
    container_name: career-coach-api
    build:
      context: ./api
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    env_file:
      - ./api/.env
    networks:
      - career-coach-network

  ui:
    container_name: career-coach-ui
    build:
      context: ./ui
      dockerfile: Dockerfile
    ports:
      - "8501:8501"
    environment:
      - API_BASE_URL=http://api:8000
    depends_on:
      - api
    networks:
      - career-coach-network

networks:
  career-coach-network:
    name: career-coach-network