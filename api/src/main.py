from contextlib import asynccontextmanager

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from opik.integrations.langchain import OpikTracer

# Import the sub-applications
from career_coaches.infrastructure.api import app as career_coaches_app
from philoagents.infrastructure.api import app as philoagents_app
from resume_editor.infrastructure.api import app as resume_editor_app


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Handles startup and shutdown events for the combined API."""
    # Startup code (if any) goes here
    yield
    # Shutdown code goes here
    opik_tracer = OpikTracer()
    opik_tracer.flush()


# Create the main application
app = FastAPI(
    title="AI Agents Platform",
    description="Combined API for PhiloAgents, Career Coach agents, and Resume Editor with multi-user support",
    version="1.0.0",
    lifespan=lifespan
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Mount sub-applications
app.mount("/philoagents", philoagents_app)
app.mount("/career-coaches", career_coaches_app)
app.mount("/resume-editor", resume_editor_app)


@app.get("/")
async def root():
    """Root endpoint with platform information."""
    return {
        "message": "Welcome to the AI Agents Platform",
        "services": {
            "philoagents": {
                "description": "Philosophical AI agents for deep conversations",
                "endpoint": "/philoagents",
                "docs": "/philoagents/docs"
            },
            "career_coaches": {
                "description": "Career coaching agents with specialized expertise",
                "endpoint": "/career-coaches",
                "docs": "/career-coaches/docs"
            },
            "resume_editor": {
                "description": "AI-powered resume editing and optimization",
                "endpoint": "/resume-editor",
                "docs": "/resume-editor/docs"
            }
        },
        "version": "1.0.0"
    }


@app.get("/health")
async def health_check():
    """Health check endpoint for the entire platform."""
    return {
        "status": "healthy",
        "platform": "ai_agents",
        "services": ["philoagents", "career_coaches", "resume_editor"]
    }


@app.get("/services")
async def get_services():
    """Get information about available services."""
    return {
        "philoagents": {
            "name": "PhiloAgents",
            "description": "AI agents embodying different philosophers",
            "features": ["philosophical_conversations", "rag_retrieval", "long_term_memory"],
            "endpoint": "/philoagents"
        },
        "career_coaches": {
            "name": "Career Coaches",
            "description": "Specialized career coaching agents",
            "features": ["career_assessment", "resume_building", "linkedin_optimization", "networking_strategy"],
            "endpoint": "/career-coaches"
        },
        "resume_editor": {
            "name": "Resume Editor",
            "description": "AI-powered resume editing and optimization",
            "features": ["resume_editing", "ats_optimization", "content_enhancement", "career_advice"],
            "endpoint": "/resume-editor"
        }
    }


if __name__ == "__main__":
    import uvicorn

    uvicorn.run(app, host="0.0.0.0", port=8000)
