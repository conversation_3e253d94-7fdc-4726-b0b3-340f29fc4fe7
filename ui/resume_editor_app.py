import streamlit as st
import requests
import uuid
import json
from typing import Dict, Optional, Any

# Page configuration
st.set_page_config(
    page_title="Resume Editor",
    page_icon="📄",
    layout="wide"
)

# Configuration
API_BASE_URL = "http://localhost:8000/resume-editor"  # Resume editor API
RESUME_EDITOR_API = f"{API_BASE_URL}"

def initialize_session_state():
    """Initialize session state variables."""
    if "messages" not in st.session_state:
        st.session_state.messages = []
    if "user_id" not in st.session_state:
        st.session_state.user_id = f"user_{str(uuid.uuid4())[:8]}"
    if "resume_data" not in st.session_state:
        st.session_state.resume_data = None
    if "resume_loaded" not in st.session_state:
        st.session_state.resume_loaded = False

def send_message(message: str, user_id: str, editing_context: str = "") -> Optional[str]:
    """Send a message to the resume editor API."""
    try:
        payload = {
            "message": message,
            "user_id": user_id,
            "editor_id": "resume_editor",
            "editing_context": editing_context
        }

        response = requests.post(
            f"{RESUME_EDITOR_API}/chat",
            json=payload,
            timeout=30
        )

        if response.status_code == 200:
            result = response.json()
            return result.get("response", "No response received")
        else:
            st.error(f"API Error: {response.status_code}")
            return None

    except requests.exceptions.ConnectionError:
        st.error("❌ Cannot connect to Resume Editor API server. Please ensure the API server is running.")
        st.info("Start the API server with: `python resume_editor/start_api.py` in the api/src directory")
        st.code("cd api/src && python resume_editor/start_api.py", language="bash")
        return None
    except requests.exceptions.Timeout:
        st.error("⏰ Request timed out. The API server might be overloaded.")
        return None
    except requests.exceptions.RequestException as e:
        st.error(f"🔌 Connection error: {e}")
        return None

def get_resume(user_id: str) -> Optional[Dict[str, Any]]:
    """Get resume data for a user."""
    try:
        response = requests.get(
            f"{RESUME_EDITOR_API}/resume/{user_id}",
            timeout=10
        )

        if response.status_code == 200:
            result = response.json()
            if result.get("status") == "success":
                return result.get("data")
        return None
    except requests.exceptions.RequestException:
        return None

def init_resume(user_id: str, name: str, email: str, title: str = "") -> bool:
    """Initialize a new resume for a user."""
    try:
        payload = {
            "user_id": user_id,
            "name": name,
            "email": email,
            "title": title
        }

        response = requests.post(
            f"{RESUME_EDITOR_API}/resume/init",
            json=payload,
            timeout=10
        )

        if response.status_code == 200:
            result = response.json()
            return result.get("status") == "success"
        return False
    except requests.exceptions.RequestException:
        return False

def copy_sample_resume(user_id: str) -> bool:
    """Copy sample resume for a user."""
    try:
        payload = {"user_id": user_id}

        response = requests.post(
            f"{RESUME_EDITOR_API}/resume/copy-sample",
            json=payload,
            timeout=10
        )

        if response.status_code == 200:
            result = response.json()
            return result.get("status") == "success"
        return False
    except requests.exceptions.RequestException:
        return False

def reset_conversation(user_id: str) -> bool:
    """Reset the conversation memory for a user."""
    try:
        payload = {"user_id": user_id}
        response = requests.post(
            f"{RESUME_EDITOR_API}/reset-conversation",
            json=payload,
            timeout=10
        )
        return response.status_code == 200
    except requests.exceptions.RequestException:
        return False

def display_resume_section(title: str, data: Any, icon: str = "📋"):
    """Display a resume section in a nice format."""
    if not data:
        return

    st.subheader(f"{icon} {title}")

    if isinstance(data, list):
        for item in data:
            if isinstance(item, dict):
                with st.expander(f"{item.get('name', item.get('position', item.get('degree', 'Item')))}"):
                    for key, value in item.items():
                        if value and key != 'name':
                            if isinstance(value, list):
                                st.write(f"**{key.replace('_', ' ').title()}:**")
                                for v in value:
                                    st.write(f"• {v}")
                            else:
                                st.write(f"**{key.replace('_', ' ').title()}:** {value}")
            else:
                st.write(f"• {item}")
    elif isinstance(data, dict):
        for key, value in data.items():
            if value:
                st.write(f"**{key.replace('_', ' ').title()}:** {value}")
    else:
        st.write(data)

def main():
    """Main application."""
    initialize_session_state()

    # Header
    st.title("📄 Resume Editor")
    st.markdown("AI-powered resume editing and optimization")

    # Load resume data if not already loaded
    if not st.session_state.resume_loaded:
        resume_data = get_resume(st.session_state.user_id)
        st.session_state.resume_data = resume_data
        st.session_state.resume_loaded = True

    # Create two columns: chat on left, resume on right
    col1, col2 = st.columns([1, 1])

    with col1:
        st.header("💬 Chat with Resume Editor")

        # Controls
        with st.expander("🎮 Controls & Settings"):
            col_a, col_b = st.columns(2)

            with col_a:
                if st.button("🗑️ Clear Chat"):
                    st.session_state.messages = []
                    st.rerun()

                if st.button("🔄 Reset Memory"):
                    if reset_conversation(st.session_state.user_id):
                        st.success("Memory reset!")
                        st.session_state.messages = []
                        st.rerun()
                    else:
                        st.error("Failed to reset memory")

            with col_b:
                if st.button("📄 Load Sample Resume"):
                    if copy_sample_resume(st.session_state.user_id):
                        st.success("Sample resume loaded!")
                        st.session_state.resume_loaded = False
                        st.rerun()
                    else:
                        st.error("Failed to load sample resume")

                if st.button("🔄 Refresh Resume"):
                    st.session_state.resume_loaded = False
                    st.rerun()

            # User context
            editing_context = st.text_area(
                "Editing Context:",
                placeholder="e.g., Applying for software engineer roles, need ATS optimization...",
                height=80
            )

            st.caption(f"User ID: {st.session_state.user_id}")

        # Display chat messages
        chat_container = st.container()
        with chat_container:
            for message in st.session_state.messages:
                with st.chat_message(message["role"]):
                    st.markdown(message["content"])

        # Chat input
        if prompt := st.chat_input("Ask about your resume or request changes..."):
            # Add user message to chat history
            st.session_state.messages.append({"role": "user", "content": prompt})

            # Display user message
            with st.chat_message("user"):
                st.markdown(prompt)

            # Get editor response
            with st.chat_message("assistant"):
                with st.spinner("Resume Editor is working..."):
                    response = send_message(
                        message=prompt,
                        user_id=st.session_state.user_id,
                        editing_context=editing_context
                    )

                if response:
                    st.markdown(response)
                    # Add assistant response to chat history
                    st.session_state.messages.append({"role": "assistant", "content": response})

                    # Refresh resume data after potential changes
                    st.session_state.resume_loaded = False
                else:
                    st.error("Failed to get response from editor. Please try again.")

    with col2:
        st.header("📋 Your Resume")

        if st.session_state.resume_data:
            # Display resume sections
            resume = st.session_state.resume_data

            # Basic info
            if resume.get('name'):
                st.markdown(f"## {resume['name']}")
            if resume.get('title'):
                st.markdown(f"*{resume['title']}*")

            # Contact info
            if resume.get('contact'):
                display_resume_section("Contact Information", resume['contact'], "📞")

            # Summary
            if resume.get('summary'):
                display_resume_section("Professional Summary", resume['summary'], "📝")

            # Skills
            if resume.get('skills'):
                display_resume_section("Skills", resume['skills'], "🛠️")

            # Experience
            if resume.get('experience'):
                display_resume_section("Work Experience", resume['experience'], "💼")

            # Education
            if resume.get('education'):
                display_resume_section("Education", resume['education'], "🎓")

            # Projects
            if resume.get('projects'):
                display_resume_section("Projects", resume['projects'], "🚀")

            # Raw JSON view
            with st.expander("🔍 View Raw JSON"):
                st.json(resume)

        else:
            st.info("No resume found. Let's create one!")

            with st.form("init_resume"):
                st.subheader("Initialize Your Resume")
                name = st.text_input("Full Name", placeholder="John Doe")
                email = st.text_input("Email", placeholder="<EMAIL>")
                title = st.text_input("Professional Title (Optional)", placeholder="Software Engineer")

                if st.form_submit_button("Create Resume"):
                    if name and email:
                        if init_resume(st.session_state.user_id, name, email, title):
                            st.success("Resume created successfully!")
                            st.session_state.resume_loaded = False
                            st.rerun()
                        else:
                            st.error("Failed to create resume")
                    else:
                        st.error("Please provide at least name and email")

if __name__ == "__main__":
    main()
