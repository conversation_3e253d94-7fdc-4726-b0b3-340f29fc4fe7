"""Main entry point for the resume copilot application."""

import os
import sys
import argparse
import subprocess
from pathlib import Path
from loguru import logger

from .api import start_api
from .test_agent import interactive_chat


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="Resume Copilot Application")
    
    # Create subparsers for different commands
    subparsers = parser.add_subparsers(dest="command", help="Command to run")
    
    # API server command
    api_parser = subparsers.add_parser("api", help="Start the API server")
    api_parser.add_argument(
        "--port", type=int, default=8000, help="Port to run the server on"
    )
    
    # Test agent chat command
    chat_parser = subparsers.add_parser("chat", help="Start an interactive chat with the agent")
    chat_parser.add_argument(
        "--user-id", type=str, default="test_user", help="User ID for testing"
    )
    chat_parser.add_argument(
        "--config", type=str, default="./config.json", help="Path to configuration file"
    )
    
    # Streamlit UI command
    streamlit_parser = subparsers.add_parser("streamlit", help="Start the Streamlit UI")
    streamlit_parser.add_argument(
        "--port", type=int, default=8501, help="Port to run Streamlit on"
    )
    streamlit_parser.add_argument(
        "--config", type=str, default="./config.json", help="Path to configuration file"
    )
    
    args = parser.parse_args()
    
    # If no command provided, display help
    if not args.command:
        parser.print_help()
        return
    
    # Run the selected command
    if args.command == "api":
        logger.info(f"Starting Resume Copilot server on port {args.port}")
        start_api(args.port)
    
    elif args.command == "chat":
        import asyncio
        logger.info(f"Starting interactive chat with user ID: {args.user_id}")
        asyncio.run(interactive_chat(args.user_id, args.config))
    
    elif args.command == "streamlit":
        # Set environment variables for Streamlit
        os.environ["RESUME_CONFIG_PATH"] = args.config
        
        logger.info(f"Starting Streamlit UI on port {args.port}")
        
        # Get the path to the streamlit_app.py file
        current_dir = Path(__file__).parent
        streamlit_app_path = current_dir / "streamlit_app.py"
        
        # Launch Streamlit using subprocess
        streamlit_cmd = [
            "streamlit", "run", str(streamlit_app_path),
            "--server.port", str(args.port),
        ]
        
        try:
            subprocess.run(streamlit_cmd)
        except KeyboardInterrupt:
            logger.info("Streamlit UI stopped by user")
        except Exception as e:
            logger.error(f"Error running Streamlit: {e}")
    
    else:
        parser.print_help()


if __name__ == "__main__":
    main()
