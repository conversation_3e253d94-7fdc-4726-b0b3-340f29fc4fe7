"""FastAPI endpoints for the resume copilot agent."""

import os
from typing import Dict, List, Optional, Any

from fastapi import Depends, FastAPI, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from loguru import logger

from .agent import AgentConfig, create_agent, ChatSession
from .tools import ResumeStore
from .utils import initialize_resume, copy_sample_resume


# Pydantic models for the API
class MessageRequest(BaseModel):
    """Request model for sending a message to the agent."""
    
    user_id: str
    message: str


class MessageResponse(BaseModel):
    """Response model for a message from the agent."""
    
    response: str


class ResumeRequest(BaseModel):
    """Request model for creating or updating a resume."""
    
    user_id: str
    resume_data: Dict[str, Any]


class ResumeResponse(BaseModel):
    """Response model for resume operations."""
    
    status: str
    message: str
    data: Optional[Dict[str, Any]] = None


class InitResumeRequest(BaseModel):
    """Request model for initializing a new resume."""
    
    user_id: str
    name: str
    email: str
    title: Optional[str] = None


class CopySampleRequest(BaseModel):
    """Request model for copying the sample resume."""
    
    user_id: str


# Configure the app
def get_agent_config() -> AgentConfig:
    """Get the agent configuration."""
    try:
        # Load configuration
        config = AgentConfig()
        return config
    except Exception as e:
        # Handle missing required fields for testing purposes
        if 'groq_api_key' in str(e) and 'Field required' in str(e):
            logger.warning("GROQ API key is missing but continuing for testing purposes with a placeholder value")
            # Create config with default values for testing
            config = AgentConfig.model_construct()
            config.groq_api_key = "placeholder_key_for_testing"
            return config
        else:
            logger.error(f"Error loading agent configuration: {e}")
            raise HTTPException(
                status_code=500, detail=f"Error loading agent configuration: {str(e)}"
            )


# Global variables to be initialized when the API starts
agent_config = None
agent_app = None
checkpoint_store = None
resume_store = None

def initialize_services():
    """Initialize all services needed for the API."""
    global agent_config, agent_app, checkpoint_store, resume_store
    
    # Create agent config
    agent_config = get_agent_config()
    
    # Create resume store
    resume_store = ResumeStore(agent_config.resume_data_path)
    
    # Create agent and checkpoint store
    agent_app, checkpoint_store = create_agent(agent_config)


# Active chat sessions
chat_sessions = {}


def get_chat_session(user_id: str) -> ChatSession:
    """Get or create a chat session for a user.
    
    Args:
        user_id: User identifier
        
    Returns:
        Chat session
    """
    # Ensure services are initialized
    if agent_config is None or agent_app is None or checkpoint_store is None:
        raise HTTPException(status_code=500, detail="API services not initialized")
        
    if user_id not in chat_sessions:
        chat_sessions[user_id] = ChatSession(
            user_id=user_id,
            config=agent_config,
            agent_app=agent_app,
            checkpoint_store=checkpoint_store,
        )
    return chat_sessions[user_id]


# Create FastAPI app
app = FastAPI(
    title="Resume Copilot API",
    description="API for the Resume Copilot Agent",
    version="0.1.0",
)


# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, restrict this to your frontend domain
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.get("/")
async def root():
    """Root endpoint."""
    return {"message": "Resume Copilot API is running"}


@app.post("/chat", response_model=MessageResponse)
async def chat(request: MessageRequest):
    """Chat with the resume copilot agent.
    
    Args:
        request: Message request
        
    Returns:
        Agent response
    """
    try:
        # Get chat session
        session = get_chat_session(request.user_id)
        
        # Send message to agent
        response = await session.send_message(request.message)
        
        return MessageResponse(response=response)
    except Exception as e:
        logger.error(f"Error in chat endpoint: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/resume/{user_id}", response_model=ResumeResponse)
async def get_resume(user_id: str) -> ResumeResponse:
    """Get a user's resume.
    
    Args:
        user_id: User identifier
        
    Returns:
        Resume data
    """
    # Ensure services are initialized
    if resume_store is None:
        raise HTTPException(status_code=500, detail="API services not initialized")
        
    try:
        resume = resume_store.load_resume(user_id)
        
        if not resume:
            return ResumeResponse(
                status="error",
                message=f"No resume found for user {user_id}"
            )
        
        # Convert to dict for JSON response
        resume_data = resume.model_dump()
        
        return ResumeResponse(
            status="success",
            message="Resume retrieved",
            data=resume_data
        )
    except Exception as e:
        logger.error(f"Error retrieving resume: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/resume/init", response_model=ResumeResponse)
async def init_resume(request: InitResumeRequest):
    """Initialize a new resume for a user.
    
    Args:
        request: Resume initialization request
        
    Returns:
        Status and resume data
    """
    # Ensure services are initialized
    if resume_store is None:
        raise HTTPException(status_code=500, detail="API services not initialized")
    
    try:
        # Check if a resume already exists
        existing_resume = resume_store.load_resume(request.user_id)
        if existing_resume:
            return ResumeResponse(
                status="error",
                message=f"Resume already exists for user {request.user_id}"
            )
        
        # Create a minimal resume with just the name and contact info
        resume = utils.create_minimal_resume(
            name=request.name,
            email=request.email,
            title=request.title
        )
        
        # Save the resume
        success = resume_store.save_resume(request.user_id, resume)
        
        if success:
            return ResumeResponse(
                status="success",
                message="Resume initialized",
                data=resume.model_dump()
            )
        else:
            return ResumeResponse(
                status="error",
                message="Failed to initialize resume"
            )
    except Exception as e:
        logger.error(f"Error initializing resume: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/resume/copy-sample", response_model=ResumeResponse)
async def copy_sample(request: CopySampleRequest):
    """Copy the sample resume for a user.
    
    Args:
        request: Copy sample request
        
    Returns:
        Status
    """
    # Ensure services are initialized
    if resume_store is None or agent_config is None:
        raise HTTPException(status_code=500, detail="API services not initialized")
    
    try:
        # Check if a resume already exists
        existing_resume = resume_store.load_resume(request.user_id)
        if existing_resume:
            return ResumeResponse(
                status="error",
                message=f"Resume already exists for user {request.user_id}"
            )
        
        # Load the sample resume
        sample_resume = utils.load_sample_resume()
        if not sample_resume:
            return ResumeResponse(
                status="error",
                message="Sample resume not found"
            )
            
        # Save the sample resume for the user
        success = resume_store.save_resume(request.user_id, sample_resume)
        
        if success:
            return ResumeResponse(
                status="success",
                message="Sample resume copied successfully",
                data=sample_resume.model_dump()
            )
        else:
            return ResumeResponse(
                status="error",
                message="Failed to copy sample resume"
            )
    except Exception as e:
        logger.error(f"Error copying sample resume: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    if not agent_config:
        return {"status": "error", "message": "API services not initialized"}
    return {"status": "ok"}


def start_api(port: int = 8000):
    """Start the API server.
    
    Args:
        port: Port to run the server on
    """
    import uvicorn
    
    # Initialize all services
    initialize_services()
    
    logger.info(f"Starting API server on port {port}")
    
    uvicorn_log_config = uvicorn.config.LOGGING_CONFIG
    
    # Update the loggers configuration to avoid conflicts with loguru
    uvicorn_log_config["loggers"]["uvicorn"]["propagate"] = False
    uvicorn_log_config["loggers"]["uvicorn.access"]["propagate"] = False
    
    uvicorn.run("resume-editor.api:app", host="0.0.0.0", port=port, reload=True)
