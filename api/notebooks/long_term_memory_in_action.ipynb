{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from langchain_core.documents import Document\n", "\n", "from philoagents.application import LongTermMemoryRetriever"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["from philoagents.config import settings\n", "\n", "# Override MongoDB connection string\n", "settings.MONGO_URI = (\n", "    \"************************************************************************\"\n", ")"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["def print_memories(memories: list[Document]) -> None:\n", "    for i, memory in enumerate(memories):\n", "        print(\"-\" * 100)\n", "        print(f\"Memory {i + 1}:\")\n", "        print(f\"{i + 1}. {memory.page_content[:100]}\")\n", "        print(f\"Source: {memory.metadata['source']}\")\n", "        print(\"-\" * 100)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["retriever = LongTermMemoryRetriever.build_from_settings()\n", "\n", "memories = retriever(\"Socrates\")\n", "print_memories(memories)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["memories = retriever(\"Turing\")\n", "print_memories(memories)"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 2}