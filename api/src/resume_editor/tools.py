"""Tools for the resume copilot agent to interact with the resume data."""

import json
from datetime import date, datetime
from pathlib import Path
from typing import Dict, List, Optional, Any, Type, Union

from pydantic import BaseModel, Field
from langchain_core.tools import BaseTool, ToolException
from loguru import logger

from .domain.entities import Resume


def _serialize_date(obj):
    """Helper function to serialize date objects to string."""
    if isinstance(obj, (date, datetime)):
        return obj.isoformat()
    raise TypeError(f"Type {type(obj)} not serializable")


class ResumeStore:
    """Storage class for resume data."""

    def __init__(self, storage_path: str = "./data"):
        """Initialize the resume store.

        Args:
            storage_path: Path to store resume data
        """
        self.storage_path = Path(storage_path)
        self.storage_path.mkdir(exist_ok=True, parents=True)

    def _get_resume_path(self, user_id: str) -> Path:
        """Get the path to a user's resume file.

        Args:
            user_id: User identifier

        Returns:
            Path to the resume file
        """
        return self.storage_path / f"{user_id}.json"

    def load_resume(self, user_id: str) -> Optional[Resume]:
        """Load the resume data for a user.

        Args:
            user_id: User identifier

        Returns:
            Resume object or None if not found
        """
        resume_path = self._get_resume_path(user_id)
        if not resume_path.exists():
            return None

        try:
            with open(resume_path, "r") as f:
                resume_data = json.load(f)

            # Convert string dates back to date objects
            if "education" in resume_data:
                for edu in resume_data["education"]:
                    if "start_date" in edu:
                        edu["start_date"] = date.fromisoformat(edu["start_date"])
                    if "end_date" in edu and edu["end_date"]:
                        edu["end_date"] = date.fromisoformat(edu["end_date"])

            if "experience" in resume_data:
                for exp in resume_data["experience"]:
                    if "start_date" in exp:
                        exp["start_date"] = date.fromisoformat(exp["start_date"])
                    if "end_date" in exp and exp["end_date"]:
                        exp["end_date"] = date.fromisoformat(exp["end_date"])

            if "projects" in resume_data:
                for proj in resume_data["projects"]:
                    if "start_date" in proj and proj["start_date"]:
                        proj["start_date"] = date.fromisoformat(proj["start_date"])
                    if "end_date" in proj and proj["end_date"]:
                        proj["end_date"] = date.fromisoformat(proj["end_date"])

            return Resume(**resume_data)
        except Exception as e:
            logger.error(f"Error loading resume for user {user_id}: {e}")
            return None

    def save_resume(self, user_id: str, resume: Resume) -> bool:
        """Save the resume data for a user.

        Args:
            user_id: User identifier
            resume: Resume object

        Returns:
            True if successful, False otherwise
        """
        resume_path = self._get_resume_path(user_id)
        try:
            with open(resume_path, "w") as f:
                json.dump(resume.dict(), f, default=_serialize_date, indent=2)
            return True
        except Exception as e:
            logger.error(f"Error saving resume for user {user_id}: {e}")
            return False

    def update_resume_section(self, user_id: str, section: str, data: Any) -> bool:
        """Update a specific section of the resume.

        Args:
            user_id: User identifier
            section: Section name to update
            data: New data for the section

        Returns:
            True if successful, False otherwise
        """
        resume = self.load_resume(user_id)
        if not resume:
            return False

        try:
            # Convert to a dict for easier manipulation
            resume_dict = resume.model_dump()  # Use model_dump() instead of dict() for newer Pydantic
            resume_dict[section] = data

            # Create a new Resume object and save
            updated_resume = Resume(**resume_dict)
            return self.save_resume(user_id, updated_resume)
        except Exception as e:
            logger.error(f"Error updating resume section {section} for user {user_id}: {e}")
            return False


# Tool input schemas
class GetResumeInput(BaseModel):
    """Input for the get_resume tool."""

    user_id: str = Field(..., description="The user's identifier")


class UpdateResumeInput(BaseModel):
    """Input for the update_resume tool."""

    user_id: str = Field(..., description="The user's identifier")
    resume: Dict = Field(..., description="The complete resume data")


class GetResumeSectionInput(BaseModel):
    """Input for the get_resume_section tool."""

    user_id: str = Field(..., description="The user's identifier")
    section: str = Field(..., description="The section to retrieve (e.g., education, experience, skills)")


class UpdateResumeSectionInput(BaseModel):
    """Input for the update_resume_section tool."""

    user_id: str = Field(..., description="The user's identifier")
    section: str = Field(..., description="The section to update (e.g., education, experience, skills)")
    data: Any = Field(..., description="The new data for the section")


# Resume tools
class GetResumeTool(BaseTool):
    """Tool to get the complete resume."""

    def __init__(self, resume_store: ResumeStore):
        """Initialize the tool with a resume store."""
        super().__init__(
            name="get_resume",
            description="Get the complete resume data for a user.",
            args_schema=GetResumeInput
        )
        self._resume_store = resume_store

    def _run(self, user_id: str) -> Dict:
        """Run the tool."""
        resume = self._resume_store.load_resume(user_id)
        if not resume:
            return {"error": f"No resume found for user {user_id}"}
        return resume.model_dump()  # Use model_dump() instead of dict() for newer Pydantic


class UpdateResumeTool(BaseTool):
    """Tool to update the complete resume."""

    def __init__(self, resume_store: ResumeStore):
        """Initialize the tool with a resume store."""
        super().__init__(
            name="update_resume",
            description="Update the complete resume for a user.",
            args_schema=UpdateResumeInput
        )
        self._resume_store = resume_store

    def _run(self, user_id: str, resume: Dict) -> Dict:
        """Run the tool."""
        try:
            # Convert JSON dates to date objects
            if "education" in resume:
                for edu in resume["education"]:
                    if "start_date" in edu and isinstance(edu["start_date"], str):
                        edu["start_date"] = date.fromisoformat(edu["start_date"])
                    if "end_date" in edu and edu["end_date"] and isinstance(edu["end_date"], str):
                        edu["end_date"] = date.fromisoformat(edu["end_date"])

            if "experience" in resume:
                for exp in resume["experience"]:
                    if "start_date" in exp and isinstance(exp["start_date"], str):
                        exp["start_date"] = date.fromisoformat(exp["start_date"])
                    if "end_date" in exp and exp["end_date"] and isinstance(exp["end_date"], str):
                        exp["end_date"] = date.fromisoformat(exp["end_date"])

            if "projects" in resume:
                for proj in resume["projects"]:
                    if "start_date" in proj and proj["start_date"] and isinstance(proj["start_date"], str):
                        proj["start_date"] = date.fromisoformat(proj["start_date"])
                    if "end_date" in proj and proj["end_date"] and isinstance(proj["end_date"], str):
                        proj["end_date"] = date.fromisoformat(proj["end_date"])

            resume_obj = Resume(**resume)
            success = self._resume_store.save_resume(user_id, resume_obj)

            if success:
                return {"status": "success", "message": "Resume updated successfully"}
            else:
                return {"status": "error", "message": "Failed to update resume"}
        except Exception as e:
            raise ToolException(f"Error updating resume: {str(e)}")


class GetResumeSectionTool(BaseTool):
    """Tool to get a specific section of the resume."""

    def __init__(self, resume_store: ResumeStore):
        """Initialize the tool with a resume store."""
        super().__init__(
            name="get_resume_section",
            description="Get a specific section of the resume for a user.",
            args_schema=GetResumeSectionInput
        )
        self._resume_store = resume_store

    def _run(self, user_id: str, section: str) -> Dict:
        """Run the tool."""
        # Get the resume
        resume = self._resume_store.load_resume(user_id)
        if not resume:
            return {"error": f"No resume found for user {user_id}"}

        # Check if the section exists
        if not hasattr(resume, section):
            return {"error": f"Section '{section}' not found in resume"}

        section_data = getattr(resume, section)

        # Convert to dict if it's a Pydantic model
        if hasattr(section_data, "model_dump"):
            section_data = section_data.model_dump()

        # Handle lists of Pydantic models
        if isinstance(section_data, list) and section_data and hasattr(section_data[0], "model_dump"):
            section_data = [item.model_dump() for item in section_data]

        return section_data


class UpdateResumeSectionTool(BaseTool):
    """Tool to update a specific section of the resume."""

    def __init__(self, resume_store: ResumeStore):
        """Initialize the tool with a resume store."""
        super().__init__(
            name="update_resume_section",
            description="Update a specific section of the resume for a user.",
            args_schema=UpdateResumeSectionInput
        )
        self._resume_store = resume_store

    def _run(self, user_id: str, section: str, data: Any) -> Dict:
        """Run the tool."""
        success = self._resume_store.update_resume_section(user_id, section, data)

        if success:
            return {"status": "success", "message": f"Section {section} updated successfully"}
        else:
            return {"status": "error", "message": f"Failed to update section {section}"}


def get_resume_tools(resume_store: ResumeStore) -> List[BaseTool]:
    """Get all resume tools.

    Args:
        resume_store: Resume storage

    Returns:
        List of resume tools
    """
    return [
        GetResumeTool(resume_store),
        UpdateResumeTool(resume_store),
        GetResumeSectionTool(resume_store),
        UpdateResumeSectionTool(resume_store),
    ]
