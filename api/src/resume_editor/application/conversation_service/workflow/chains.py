from langchain_core.prompts import Chat<PERSON>romptTemplate, MessagesPlaceholder
from langchain_groq import Chat<PERSON><PERSON><PERSON>

from resume_editor.config import settings
from resume_editor.domain.prompts import (
    RESUME_COPILOT_SYSTEM_PROMPT,
    RESUME_EDITING_PROMPT,
    INTENT_CLASSIFICATION_PROMPT,
    EDIT_INTERPRETATION_PROMPT,
    TOOL_CALL_GENERATION_PROMPT,
    CHANGE_SUMMARY_PROMPT,
    RESUME_BEST_PRACTICES_PROMPT,
    ATS_OPTIMIZATION_PROMPT,
)
from common.domain.prompts import (
    CONTEXT_SUMMARY_PROMPT,
    EXTEND_SUMMARY_PROMPT,
    SUMMARY_PROMPT,
)


def get_chat_model(temperature: float = settings.LLM_TEMPERATURE, model_name: str = settings.GROQ_LLM_MODEL) -> ChatGroq:
    """Get a configured ChatGroq model instance."""
    return ChatGroq(
        api_key=settings.GROQ_API_KEY,
        model_name=model_name,
        temperature=temperature,
    )


def get_resume_editor_chain():
    """Get the main resume editor conversation chain."""
    prompt = ChatPromptTemplate.from_messages([
        ("system", RESUME_COPILOT_SYSTEM_PROMPT.template),
        MessagesPlaceholder(variable_name="messages"),
    ])
    
    llm = get_chat_model()
    return prompt | llm


def get_intent_classification_chain():
    """Get the intent classification chain."""
    prompt = ChatPromptTemplate.from_messages([
        ("system", "You classify user intents for resume interactions."),
        ("user", INTENT_CLASSIFICATION_PROMPT.template),
    ])
    
    llm = get_chat_model(temperature=0.1)  # Lower temperature for classification
    return prompt | llm


def get_edit_interpretation_chain():
    """Get the edit interpretation chain."""
    prompt = ChatPromptTemplate.from_messages([
        ("system", EDIT_INTERPRETATION_PROMPT.template),
    ])
    
    llm = get_chat_model()
    return prompt | llm


def get_tool_call_generation_chain():
    """Get the tool call generation chain."""
    prompt = ChatPromptTemplate.from_messages([
        ("system", TOOL_CALL_GENERATION_PROMPT.template),
    ])
    
    llm = get_chat_model(temperature=0.1)  # Lower temperature for structured output
    return prompt | llm


def get_change_summary_chain():
    """Get the change summary chain."""
    prompt = ChatPromptTemplate.from_messages([
        ("system", CHANGE_SUMMARY_PROMPT.template),
    ])
    
    llm = get_chat_model()
    return prompt | llm


def get_best_practices_chain():
    """Get the resume best practices advice chain."""
    prompt = ChatPromptTemplate.from_messages([
        ("system", RESUME_BEST_PRACTICES_PROMPT.template),
        MessagesPlaceholder(variable_name="messages"),
    ])
    
    llm = get_chat_model()
    return prompt | llm


def get_ats_optimization_chain():
    """Get the ATS optimization advice chain."""
    prompt = ChatPromptTemplate.from_messages([
        ("system", ATS_OPTIMIZATION_PROMPT.template),
        MessagesPlaceholder(variable_name="messages"),
    ])
    
    llm = get_chat_model()
    return prompt | llm


def get_summarization_chain():
    """Get the conversation summarization chain."""
    prompt = ChatPromptTemplate.from_messages([
        ("system", SUMMARY_PROMPT.template),
    ])
    
    llm = get_chat_model(temperature=0.3)
    return prompt | llm


def get_context_summary_chain():
    """Get the context summarization chain."""
    prompt = ChatPromptTemplate.from_messages([
        ("system", CONTEXT_SUMMARY_PROMPT.template),
    ])
    
    llm = get_chat_model(temperature=0.3)
    return prompt | llm


def get_extend_summary_chain():
    """Get the extend summary chain."""
    prompt = ChatPromptTemplate.from_messages([
        ("system", EXTEND_SUMMARY_PROMPT.template),
    ])
    
    llm = get_chat_model(temperature=0.3)
    return prompt | llm
