from functools import lru_cache

from langgraph.graph import <PERSON><PERSON>, START, StateGraph

from .edges import (
    should_summarize_conversation,
    route_by_intent,
    should_continue_editing,
    should_continue_conversation,
)
from .nodes import (
    conversation_node,
    intent_classification_node,
    resume_editing_node,
    summarize_conversation_node,
    connector_node,
)
from .state import ResumeEditorState


@lru_cache(maxsize=1)
def create_resume_editor_workflow_graph():
    """Create the resume editor workflow graph.
    
    This workflow includes:
    START -> intent_classification_node -> (conditional) resume_editing_node or conversation_node -> connector_node -> (conditional) summarize or END
    """
    graph_builder = StateGraph(ResumeEditorState)

    # Add all nodes
    graph_builder.add_node("intent_classification_node", intent_classification_node)
    graph_builder.add_node("conversation_node", conversation_node)
    graph_builder.add_node("resume_editing_node", resume_editing_node)
    graph_builder.add_node("summarize_conversation_node", summarize_conversation_node)
    graph_builder.add_node("connector_node", connector_node)
    
    # Define the workflow flow
    graph_builder.add_edge(START, "intent_classification_node")
    
    # Route based on intent classification
    graph_builder.add_conditional_edges(
        "intent_classification_node",
        route_by_intent,
        {
            "resume_editing_node": "resume_editing_node",
            "conversation_node": "conversation_node",
        }
    )
    
    # Both editing and conversation nodes go to connector
    graph_builder.add_conditional_edges(
        "resume_editing_node",
        should_continue_editing,
        {
            "connector_node": "connector_node",
            "__end__": END,
        }
    )
    
    graph_builder.add_conditional_edges(
        "conversation_node",
        should_continue_conversation,
        {
            "connector_node": "connector_node",
            "__end__": END,
        }
    )
    
    # Connector node decides whether to summarize or end
    graph_builder.add_conditional_edges(
        "connector_node",
        should_summarize_conversation,
        {
            "summarize_conversation_node": "summarize_conversation_node",
            "__end__": END,
        }
    )
    
    # Summarization leads to end
    graph_builder.add_edge("summarize_conversation_node", END)
    
    return graph_builder


# Compiled without a checkpointer. Used for LangGraph Studio
graph = create_resume_editor_workflow_graph().compile()
