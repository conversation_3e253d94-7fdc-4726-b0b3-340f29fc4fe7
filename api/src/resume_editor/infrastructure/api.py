from contextlib import asynccontextmanager
from typing import Dict, List, Optional, Any

from fastapi import FastAP<PERSON>, HTTPException, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from opik.integrations.langchain import OpikTracer
from pydantic import BaseModel
from loguru import logger

from resume_editor.application.conversation_service.generate_response import (
    get_response,
    get_streaming_response,
)
from resume_editor.application.conversation_service.reset_conversation import (
    reset_conversation_state,
)
from resume_editor.config import settings
from resume_editor.domain.editor_factory import EditorFactory
from resume_editor.tools import ResumeStore
from resume_editor import utils
from common.infrastructure.opik_utils import configure

configure(settings.COMET_API_KEY, settings.RESUME_EDITOR_PROJECT)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Handles startup and shutdown events for the Resume Editor API."""
    # Startup code (if any) goes here
    yield
    # Shutdown code goes here
    opik_tracer = OpikTracer()
    opik_tracer.flush()


app = FastAPI(
    title="Resume Editor API",
    description="API for resume editing agents with multi-user support",
    version="1.0.0",
    lifespan=lifespan
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# Pydantic models for the API
class ChatMessage(BaseModel):
    """Request model for chat messages."""
    user_id: str
    message: str
    editor_id: str = "resume_editor"
    editing_context: str = ""


class MessageResponse(BaseModel):
    """Response model for a message from the agent."""
    response: str


class ResumeRequest(BaseModel):
    """Request model for creating or updating a resume."""
    user_id: str
    resume_data: Dict[str, Any]


class ResumeResponse(BaseModel):
    """Response model for resume operations."""
    status: str
    message: str
    data: Optional[Dict[str, Any]] = None


class InitResumeRequest(BaseModel):
    """Request model for initializing a new resume."""
    user_id: str
    name: str
    email: str
    title: Optional[str] = None


class CopySampleRequest(BaseModel):
    """Request model for copying the sample resume."""
    user_id: str


class ResetConversationRequest(BaseModel):
    """Request model for resetting conversation."""
    user_id: str


# Global resume store
resume_store = None


def get_resume_store() -> ResumeStore:
    """Get or create the resume store."""
    global resume_store
    if resume_store is None:
        resume_store = ResumeStore(settings.RESUME_DATA_PATH)
    return resume_store


@app.post("/chat")
async def chat(chat_message: ChatMessage):
    """Chat endpoint for resume editing conversations."""
    try:
        editor_factory = EditorFactory()
        editor = editor_factory.get_editor(chat_message.editor_id)

        response, _ = await get_response(
            messages=chat_message.message,
            user_id=chat_message.user_id,
            editor_id=chat_message.editor_id,
            editor_name=editor.name,
            editor_specialty=editor.specialty,
            editor_approach=editor.approach,
            editor_focus_areas=", ".join(editor.focus_areas),
            editing_context=chat_message.editing_context,
        )
        print(response)
        return {"response": response}
    except Exception as e:
        opik_tracer = OpikTracer()
        opik_tracer.flush()
        raise HTTPException(status_code=500, detail=str(e))


@app.websocket("/ws/chat")
async def websocket_chat(websocket: WebSocket):
    """WebSocket endpoint for streaming resume editing conversations."""
    await websocket.accept()

    try:
        while True:
            data = await websocket.receive_json()

            required_fields = ["message", "editor_id", "user_id"]
            if not all(field in data for field in required_fields):
                await websocket.send_json(
                    {
                        "error": f"Invalid message format. Required fields: {required_fields}"
                    }
                )
                continue

            try:
                editor_factory = EditorFactory()
                editor = editor_factory.get_editor(data["editor_id"])

                async for chunk in get_streaming_response(
                    messages=data["message"],
                    user_id=data["user_id"],
                    editor_id=data["editor_id"],
                    editor_name=editor.name,
                    editor_specialty=editor.specialty,
                    editor_approach=editor.approach,
                    editor_focus_areas=", ".join(editor.focus_areas),
                    editing_context=data.get("editing_context", ""),
                ):
                    if isinstance(chunk, dict):
                        # Metadata chunk
                        await websocket.send_json(chunk)
                    else:
                        # Text chunk
                        await websocket.send_json({"chunk": chunk})

            except Exception as e:
                await websocket.send_json({"error": str(e)})
                break

    except WebSocketDisconnect:
        logger.info("WebSocket client disconnected")
    except Exception as e:
        logger.error(f"WebSocket error: {e}")
        await websocket.close()


@app.post("/reset-conversation")
async def reset_conversation(request: ResetConversationRequest):
    """Reset conversation state for a user."""
    try:
        success = await reset_conversation_state(request.user_id)
        if success:
            return {"status": "success", "message": "Conversation reset successfully"}
        else:
            return {"status": "error", "message": "Failed to reset conversation"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/resume/{user_id}", response_model=ResumeResponse)
async def get_resume(user_id: str) -> ResumeResponse:
    """Get a user's resume."""
    try:
        store = get_resume_store()
        resume = store.load_resume(user_id)
        
        if not resume:
            return ResumeResponse(
                status="error",
                message=f"No resume found for user {user_id}"
            )
        
        resume_data = resume.model_dump()
        
        return ResumeResponse(
            status="success",
            message="Resume retrieved",
            data=resume_data
        )
    except Exception as e:
        logger.error(f"Error retrieving resume: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/resume/init", response_model=ResumeResponse)
async def init_resume(request: InitResumeRequest):
    """Initialize a new resume for a user."""
    try:
        store = get_resume_store()
        
        # Check if a resume already exists
        existing_resume = store.load_resume(request.user_id)
        if existing_resume:
            return ResumeResponse(
                status="error",
                message=f"Resume already exists for user {request.user_id}"
            )
        
        # Create a minimal resume with just the name and contact info
        resume = utils.create_minimal_resume(
            name=request.name,
            email=request.email,
            title=request.title
        )
        
        # Save the resume
        success = store.save_resume(request.user_id, resume)
        
        if success:
            return ResumeResponse(
                status="success",
                message="Resume initialized",
                data=resume.model_dump()
            )
        else:
            return ResumeResponse(
                status="error",
                message="Failed to initialize resume"
            )
    except Exception as e:
        logger.error(f"Error initializing resume: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/resume/copy-sample", response_model=ResumeResponse)
async def copy_sample(request: CopySampleRequest):
    """Copy the sample resume for a user."""
    try:
        store = get_resume_store()
        
        # Check if a resume already exists
        existing_resume = store.load_resume(request.user_id)
        if existing_resume:
            return ResumeResponse(
                status="error",
                message=f"Resume already exists for user {request.user_id}"
            )
        
        # Load the sample resume
        sample_resume = utils.load_sample_resume()
        if not sample_resume:
            return ResumeResponse(
                status="error",
                message="Sample resume not found"
            )
            
        # Save the sample resume for the user
        success = store.save_resume(request.user_id, sample_resume)
        
        if success:
            return ResumeResponse(
                status="success",
                message="Sample resume copied successfully",
                data=sample_resume.model_dump()
            )
        else:
            return ResumeResponse(
                status="error",
                message="Failed to copy sample resume"
            )
    except Exception as e:
        logger.error(f"Error copying sample resume: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/editors")
async def get_available_editors():
    """Get list of available resume editors."""
    try:
        editor_factory = EditorFactory()
        available_editors = editor_factory.get_available_editors()
        
        editors_info = []
        for editor_id in available_editors:
            editor = editor_factory.get_editor(editor_id)
            editors_info.append({
                "id": editor.id,
                "name": editor.name,
                "specialty": editor.specialty,
                "focus_areas": editor.focus_areas
            })
        
        return {"editors": editors_info}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "healthy", "service": "resume_editor"}


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8002)
