from typing import Literal

from .state import ResumeEditorState
from resume_editor.config import settings


def should_summarize_conversation(state: ResumeEditorState) -> Literal["summarize_conversation_node", "__end__"]:
    """Determine if the conversation should be summarized based on message count."""
    messages = state.get("messages", [])
    
    # Check if we should summarize based on total message count
    if len(messages) >= settings.TOTAL_MESSAGES_SUMMARY_TRIGGER:
        return "summarize_conversation_node"
    
    return "__end__"


def route_by_intent(state: ResumeEditorState) -> Literal["resume_editing_node", "conversation_node"]:
    """Route the conversation based on user intent."""
    intent = state.get("intent", "chat")
    
    if intent == "edit":
        return "resume_editing_node"
    else:
        return "conversation_node"


def should_continue_editing(state: ResumeEditorState) -> Literal["connector_node", "__end__"]:
    """Determine if editing workflow should continue or end."""
    # For now, always go to connector node to check for summarization
    return "connector_node"


def should_continue_conversation(state: ResumeEditorState) -> Literal["connector_node", "__end__"]:
    """Determine if conversation workflow should continue or end."""
    # For now, always go to connector node to check for summarization
    return "connector_node"
