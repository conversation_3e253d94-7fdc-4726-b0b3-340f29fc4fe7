"""Resume Copilot Agent implementation using LangGraph."""

import json
import os
from typing import Dict, List, Optional, Tuple, Any, Annotated, Sequence, TypedDict, cast, Type, Union

from langchain_core.messages import AIMessage, BaseMessage, HumanMessage
from langchain_core.output_parsers import Str<PERSON>utputParser
from langchain_core.prompts import Chat<PERSON>romptTemplate, MessagesPlaceholder
from langchain_core.runnables import Runnable, RunnableConfig
from langchain_core.tools import BaseTool
from langchain_groq import ChatGroq
from langchain_community.chat_message_histories import ChatMessageHistory
from langgraph.checkpoint.memory import MemorySaver
from langgraph.graph import END, MessagesState, StateGraph, add_messages
from langgraph.prebuilt import ToolNode
from loguru import logger
from pydantic import BaseModel
from pydantic_settings import BaseSettings, SettingsConfigDict

# Import MongoDB modules conditionally
try:
    from pymongo import MongoClient
    from langgraph.checkpoint.mongodb import MongoDBCheckpointStore
    MONGODB_AVAILABLE = True
except ImportError:
    logger.warning("MongoDB modules not available. Will use in-memory checkpoint store.")
    MONGODB_AVAILABLE = False

from .tools import ResumeStore, get_resume_tools, _serialize_date


class AgentConfig(BaseSettings):
    """Configuration for the resume copilot agent."""
    
    model_config = SettingsConfigDict(env_prefix="RESUME_AGENT_")
    
    # LLM configuration
    groq_api_key: str = "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"
    model_name: str = "llama-3.3-70b-versatile"
    temperature: float = 0.3
    
    # MongoDB configuration for chat history (optional)
    mongodb_uri: str = "mongodb+srv://resumeuser:<EMAIL>/"
    mongodb_db_name: str = "resume_copilot"
    chat_history_collection: str = "chat_histories"
    checkpoints_collection: str = "checkpoints"
    
    # Resume data storage
    resume_data_path: str = "./data"


class AgentState(MessagesState):
    """State for the resume copilot agent."""
    
    user_id: str


def create_agent(config: AgentConfig):
    """Create a resume copilot agent.
    
    Args:
        config: Agent configuration
        
    Returns:
        Tuple of agent app and checkpoint store
    """
    # Create LLM
    llm = ChatGroq(
        api_key=config.groq_api_key,
        model_name=config.model_name,
        temperature=config.temperature,
    )
    
    # Create tools
    # Convert to absolute path if it's relative
    data_path = config.resume_data_path
    if not os.path.isabs(data_path):
        data_path = os.path.abspath(data_path)
    logger.info(f"Using resume data path: {data_path}")
    resume_store = ResumeStore(data_path)
    resume_tools = get_resume_tools(resume_store)
    
    # Create checkpoint store - use in-memory if MongoDB not available or not configured
    checkpoint_store = None
    if config.mongodb_uri and MONGODB_AVAILABLE:
        try:
            logger.info("Attempting to connect to MongoDB for checkpoint storage")
            # Connect to MongoDB for storing chat history and checkpoints
            client = MongoClient(config.mongodb_uri)
            db = client[config.mongodb_db_name]
            
            # Create MongoDB checkpoint store
            checkpoint_store = MongoDBCheckpointStore(
                db[config.checkpoints_collection]
            )
            logger.info("Successfully connected to MongoDB")
        except Exception as e:
            logger.warning(f"Failed to connect to MongoDB: {e}. Will use in-memory checkpoint store.")
            checkpoint_store = None
    
    # Fall back to in-memory checkpoint store if MongoDB failed or wasn't configured
    if checkpoint_store is None:
        logger.info("Using in-memory checkpoint store")
        checkpoint_store = MemorySaver()
        
    # Create a tool-calling agent to work with the tools
    system_prompt = """You are a helpful Resume Copilot assistant. You help users improve their resumes by 
    providing suggestions and making updates directly to their resume.

    You have the ability to view and modify the user's resume data. You can:
    1. View the entire resume
    2. Update the entire resume
    3. View specific sections of the resume (like education, experience, skills)
    4. Update specific sections of the resume

    When a user asks you to make changes to their resume, first confirm the specific changes they want,
    then use the appropriate tool to update their resume data.

    Always tell the user what changes you've made to their resume after making them.
    Provide helpful career advice and resume best practices when appropriate.
    
    When making suggestions, be specific and tailor your advice to the user's industry and career level.
    If something is unclear or missing from the resume, ask clarifying questions.

    User ID: {user_id}
    """
    
    # Set up prompt
    prompt = ChatPromptTemplate.from_messages(
        [
            ("system", system_prompt),
            MessagesPlaceholder(variable_name="messages"),
            MessagesPlaceholder(variable_name="agent_scratchpad"),
        ]
    )
    
    # Define a simplified agent function
    def agent(state: AgentState, config: Optional[RunnableConfig] = None) -> Dict:
        """Agent function that decides what action to take."""
        # Extract messages and user_id from state
        messages = state["messages"]
        user_id = state["user_id"]
        
        # Get the latest user message
        latest_message = None
        for msg in reversed(messages):
            if isinstance(msg, HumanMessage):
                latest_message = msg.content
                break
        
        if not latest_message:
            # No user message found, return generic response
            response = "I'm ready to help with your resume. What would you like to do?"
            return {"messages": messages + [AIMessage(content=response)], "next": END}
        
        # Check for simple greetings or basic queries that should trigger resume retrieval
        lower_message = latest_message.lower()
        simple_triggers = ["hi", "hello", "hey", "resume", "about my resume", "tell me", "show me"]
        
        # If this is a very simple greeting or basic resume query, go directly to action node
        # to show the resume information
        for trigger in simple_triggers:
            if trigger in lower_message and len(lower_message.split()) < 5:
                logger.info(f"Simple trigger detected in message: '{latest_message}'")
                # Generate a simple response that will trigger the get_resume tool in the action node
                response = "I'll help you with your resume. Let me retrieve your resume information."
                return {"messages": messages + [AIMessage(content=response)], "next": "action"}
        
        # Format the system message with the user ID
        formatted_system = system_prompt.format(user_id=user_id)
        
        # Create a simpler conversation history for the LLM
        conversation = []
        conversation.append({"role": "system", "content": formatted_system})
        
        # Add relevant user/assistant message pairs to conversation
        for msg in messages:
            if isinstance(msg, HumanMessage):
                conversation.append({"role": "user", "content": msg.content})
            elif isinstance(msg, AIMessage):
                conversation.append({"role": "assistant", "content": msg.content})
        
        # Invoke the LLM directly with the conversation history
        try:
            llm_response = llm.invoke(conversation)
            
            # Extract content from the response
            if isinstance(llm_response, AIMessage):
                response_text = llm_response.content
            else:
                response_text = str(llm_response)
            
            # Check for tool usage and prepare for execution
            # Look for patterns like "get_resume(user_id)" or similar tool invocations
            tool_call_detected = False
            
            for tool in resume_tools:
                if tool.name in response_text:
                    tool_call_detected = True
                    break
                    
            # Add specific check for get_resume which is likely what we want to use first
            if "get_resume" in response_text or "view resume" in response_text.lower() or "get the resume" in response_text.lower():
                tool_call_detected = True
                
            if tool_call_detected:
                # Log that we detected a tool call
                logger.info(f"Tool call detected in response: {response_text[:100]}...")
                # If tool usage is detected, continue to action node
                return {"messages": messages + [AIMessage(content=response_text)], "next": "action"}
            
            # No tool usage, so return the response directly
            return {"messages": messages + [AIMessage(content=response_text)], "next": END}
            
        except Exception as e:
            # Handle any errors during LLM invocation
            error_msg = f"Error processing your request: {str(e)}"
            logger.error(f"LLM error: {e}")
            return {"messages": messages + [AIMessage(content=error_msg)], "next": END}
    
    # Define a custom action node to properly handle tool results
    def action_node(state: AgentState, config: Optional[RunnableConfig] = None) -> Dict:
        """Custom action node that runs tools and ensures proper completion."""
        messages = state["messages"]
        
        # Run tools
        try:
            # Get the last message which should contain the tool call
            last_message = messages[-1]
            if not isinstance(last_message, AIMessage):
                # Something went wrong, return to agent with error
                error_msg = "Failed to process tool call properly."
                return {"messages": messages + [AIMessage(content=error_msg)], "next": END}
            
            # Get user_id from state
            user_id = state["user_id"]
            logger.info(f"Processing action for user_id: {user_id}")
            
            # Check if the message contains specific requests about the resume
            message_text = last_message.content.lower()
            user_message = ""
            
            # Find the latest user message to analyze the intent
            for msg in reversed(messages[:-1]):  # Skip the AI message we just added
                if isinstance(msg, HumanMessage):
                    user_message = msg.content.lower()
                    break
            
            logger.info(f"Processing user message: {user_message}")
            
            # Check if the message is asking to update skills
            if "add" in user_message and "skill" in user_message:
                # User wants to add a skill
                return handle_add_skill(user_id, user_message, messages)
            
            # Check for other specific section updates
            if any(section in user_message for section in ["update", "change", "modify", "add"]) and any(section in user_message for section in ["experience", "education", "project", "summary", "contact"]):
                # User wants to update a specific section
                return handle_section_update(user_id, user_message, messages)
            
            # Check for specific queries about resume sections
            # Handle project queries
            if any(term in user_message for term in ["project", "projects", "portfolio", "what have i built", "what have i made"]):
                for tool in resume_tools:
                    if tool.name == "get_resume":
                        try:
                            logger.info(f"Retrieving projects for user {user_id}")
                            result = tool.run(user_id)
                            
                            if "error" in result or "projects" not in result or not result["projects"]:
                                tool_result = "I couldn't find any projects listed in your resume. Would you like to add some project experience?"
                            else:
                                project_response = "Here are the projects from your resume:\n\n"
                                
                                for project in result["projects"]:
                                    project_response += f"**{project['name']}**\n"
                                    if "description" in project and project["description"]:
                                        project_response += f"Description: {project['description']}\n"
                                    if "url" in project and project["url"]:
                                        project_response += f"URL: {project['url']}\n"
                                    if "start_date" in project and project["start_date"]:
                                        if "end_date" in project and project["end_date"]:
                                            project_response += f"Duration: {project['start_date']} to {project['end_date']}\n"
                                        else:
                                            project_response += f"Started: {project['start_date']} (Ongoing)\n"
                                    if "highlights" in project and project["highlights"]:
                                        project_response += "Key highlights:\n"
                                        for highlight in project["highlights"]:
                                            project_response += f"- {highlight}\n"
                                    project_response += "\n"
                                
                                project_response += "Would you like to add more projects or update the existing ones?"
                                tool_result = project_response
                            
                            return {"messages": messages + [AIMessage(content=tool_result)], "next": END}
                        except Exception as e:
                            error_msg = f"Error retrieving projects: {str(e)}"
                            logger.error(f"Error getting projects: {str(e)}")
                            return {"messages": messages + [AIMessage(content=error_msg)], "next": END}
            
            # Handle skills queries
            if any(term in user_message for term in ["skill", "skills", "what can i do", "what am i good at"]):
                for tool in resume_tools:
                    if tool.name == "get_resume":
                        try:
                            logger.info(f"Retrieving skills for user {user_id}")
                            result = tool.run(user_id)
                            
                            if "error" in result or "skills" not in result or not result["skills"]:
                                tool_result = "I couldn't find any skills listed in your resume. Would you like to add some?"
                            else:
                                skill_response = "Based on your resume, here are your skills:\n\n"
                                
                                for skill in result["skills"]:
                                    skill_response += f"• {skill['name']} - {skill['level']}\n"
                                
                                skill_response += "\nWould you like to add more skills or update any of these?"
                                tool_result = skill_response
                            
                            return {"messages": messages + [AIMessage(content=tool_result)], "next": END}
                        except Exception as e:
                            error_msg = f"Error retrieving skills: {str(e)}"
                            logger.error(f"Error getting skills: {str(e)}")
                            return {"messages": messages + [AIMessage(content=error_msg)], "next": END}
            
            # Handle queries about education
            elif any(term in user_message for term in ["education", "degree", "university", "college", "school"]):
                for tool in resume_tools:
                    if tool.name == "get_resume":
                        try:
                            logger.info(f"Retrieving education for user {user_id}")
                            result = tool.run(user_id)
                            
                            if "error" in result or "education" not in result or not result["education"]:
                                tool_result = "I couldn't find any education details in your resume. Would you like to add your educational background?"
                            else:
                                edu_response = "Here's your education background from your resume:\n\n"
                                
                                for edu in result["education"]:
                                    edu_response += f"• {edu['degree']} in {edu['field_of_study']} from {edu['institution']}\n"
                                    if "start_date" in edu and edu["start_date"] and "end_date" in edu and edu["end_date"]:
                                        edu_response += f"  ({edu['start_date']} to {edu['end_date']})\n"
                                    if "gpa" in edu and edu["gpa"]:
                                        edu_response += f"  GPA: {edu['gpa']}\n"
                                    if "description" in edu and edu["description"]:
                                        edu_response += f"  {edu['description']}\n"
                                    edu_response += "\n"
                                
                                edu_response += "Would you like to add more education details or update the existing ones?"
                                tool_result = edu_response
                            
                            return {"messages": messages + [AIMessage(content=tool_result)], "next": END}
                        except Exception as e:
                            error_msg = f"Error retrieving education details: {str(e)}"
                            logger.error(f"Error getting education: {str(e)}")
                            return {"messages": messages + [AIMessage(content=error_msg)], "next": END}
            
            # Handle queries about work experience
            elif any(term in user_message for term in ["experience", "job", "work", "career", "company", "employer"]):
                for tool in resume_tools:
                    if tool.name == "get_resume":
                        try:
                            logger.info(f"Retrieving experience for user {user_id}")
                            result = tool.run(user_id)
                            
                            if "error" in result or "experience" not in result or not result["experience"]:
                                tool_result = "I couldn't find any work experience details in your resume. Would you like to add your work history?"
                            else:
                                exp_response = "Here's your work experience from your resume:\n\n"
                                
                                for exp in result["experience"]:
                                    exp_response += f"• {exp['position']} at {exp['company']}\n"
                                    if "start_date" in exp and exp["start_date"]:
                                        if "end_date" in exp and exp["end_date"]:
                                            exp_response += f"  ({exp['start_date']} to {exp['end_date']})\n"
                                        else:
                                            exp_response += f"  (Since {exp['start_date']} - Present)\n"
                                    if "description" in exp and exp["description"]:
                                        exp_response += f"  {exp['description']}\n"
                                    if "highlights" in exp and exp["highlights"]:
                                        exp_response += "  Key achievements:\n"
                                        for highlight in exp["highlights"]:
                                            exp_response += f"    - {highlight}\n"
                                    exp_response += "\n"
                                
                                exp_response += "Would you like to add more work experience or update the existing entries?"
                                tool_result = exp_response
                            
                            return {"messages": messages + [AIMessage(content=tool_result)], "next": END}
                        except Exception as e:
                            error_msg = f"Error retrieving work experience details: {str(e)}"
                            logger.error(f"Error getting experience: {str(e)}")
                            return {"messages": messages + [AIMessage(content=error_msg)], "next": END}
            
            # Handle queries about resume summary or overview
            elif any(term in user_message for term in ["summary", "overview", "about me", "about my resume", "summarize"]):
                for tool in resume_tools:
                    if tool.name == "get_resume":
                        try:
                            logger.info(f"Retrieving summary for user {user_id}")
                            result = tool.run(user_id)
                            
                            if "error" in result:
                                tool_result = f"I tried to access your resume, but encountered an issue: {result['error']}"
                            else:
                                summary_response = "Here's a summary of your resume:\n\n"
                                
                                if "name" in result:
                                    summary_response += f"**Name:** {result['name']}\n"
                                if "title" in result and result['title']:
                                    summary_response += f"**Title:** {result['title']}\n"
                                if "summary" in result and result['summary']:
                                    summary_response += f"**Professional Summary:** {result['summary']}\n\n"
                                
                                if "skills" in result and result['skills']:
                                    summary_response += f"**Skills:** You have {len(result['skills'])} skills listed, including "
                                    top_skills = [skill['name'] for skill in result['skills'][:3]]
                                    summary_response += ", ".join(top_skills)
                                    if len(result['skills']) > 3:
                                        summary_response += ", and others"                                
                                    summary_response += ".\n"
                                
                                if "experience" in result and result['experience']:
                                    summary_response += f"**Experience:** {len(result['experience'])} positions, "
                                    latest_job = result['experience'][0]
                                    summary_response += f"most recently as {latest_job['position']} at {latest_job['company']}.\n"
                                
                                if "education" in result and result['education']:
                                    summary_response += f"**Education:** {len(result['education'])} entries, "
                                    latest_edu = result['education'][0]
                                    summary_response += f"including {latest_edu['degree']} in {latest_edu['field_of_study']} from {latest_edu['institution']}.\n"
                                
                                summary_response += "\nWhat specific part of your resume would you like to work on or learn more about?"
                                tool_result = summary_response
                            
                            return {"messages": messages + [AIMessage(content=tool_result)], "next": END}
                        except Exception as e:
                            error_msg = f"Error retrieving resume summary: {str(e)}"
                            logger.error(f"Error getting summary: {str(e)}")
                            return {"messages": messages + [AIMessage(content=error_msg)], "next": END}
            
            # Default behavior - retrieve basic resume info
            # Execute get_resume tool
            for tool in resume_tools:
                if tool.name == "get_resume":
                    try:
                        logger.info(f"Executing get_resume for user {user_id}")
                        result = tool.run(user_id)
                        
                        if "error" in result:
                            # Resume not found or error
                            tool_result = f"I tried to access your resume, but encountered an issue: {result['error']}"
                            logger.warning(f"Resume retrieval error: {result['error']}")
                        else:
                            # Successfully retrieved resume - provide a more engaging response
                            tool_result = f"Hi {result.get('name', 'there')}! I'm your Resume Copilot assistant and I have access to your resume. "
                            tool_result += "I can help you review specific sections, make updates, or provide suggestions for improvement. "
                            tool_result += "What would you like help with today? You can ask about your skills, education, work experience, or get suggestions for improvements."
                        
                        return {"messages": messages + [AIMessage(content=tool_result)], "next": END}
                    except Exception as e:
                        error_msg = f"Error retrieving resume: {str(e)}"
                        logger.error(f"Error in get_resume tool: {str(e)}")
                        return {"messages": messages + [AIMessage(content=error_msg)], "next": END}
            
            # Fallback if no tool executed
            logger.info("No specific tool matched, using fallback response")
            tool_result = "I've processed your request. "
            tool_result += "Unfortunately, I couldn't run the tool directly due to parsing limitations. "
            tool_result += "Please try rephrasing your request in a simpler way. "
            tool_result += "You can ask me to view your resume, update specific sections, or provide suggestions."
            
            # Return the result
            return {"messages": messages + [AIMessage(content=tool_result)], "next": END}
                
        except Exception as e:
            # Handle any errors during tool execution
            error_msg = f"Error running tool: {str(e)}"
            logger.error(f"Tool error: {e}")
            return {"messages": messages + [AIMessage(content=error_msg)], "next": END}
    
    # Function to route between chat and resume editing
    def router(state: AgentState, config: Optional[RunnableConfig] = None) -> Dict:
        """Route between chat and resume editing based on user intent."""
        messages = state["messages"]
        user_id = state["user_id"]
        
        # Get the latest user message
        latest_message = None
        for msg in reversed(messages):
            if isinstance(msg, HumanMessage):
                latest_message = msg.content
                break
        
        if not latest_message:
            # No user message found, return generic response
            response = "I'm ready to help with your resume. What would you like to do?"
            return {"messages": messages + [AIMessage(content=response)], "next": END}
        
        # Format the system message with the user ID
        formatted_system = system_prompt.format(user_id=user_id)
        
        # Create a simple conversation history for the LLM
        conversation = []
        conversation.append({"role": "system", "content": formatted_system})
        
        # Add relevant user/assistant message pairs to conversation
        for msg in messages:
            if isinstance(msg, HumanMessage):
                conversation.append({"role": "user", "content": msg.content})
            elif isinstance(msg, AIMessage):
                conversation.append({"role": "assistant", "content": msg.content})
        
        # Analyze user intent
        intent_query = f"Based on this user message: '{latest_message}', determine if the user wants to: 1) Edit or modify their resume (add/update/delete something) or 2) Just chat or get information about their resume without changing it. Respond with exactly 'EDIT' or 'CHAT'"
        
        try:
            # Use the LLM to determine intent
            intent_response = llm.invoke([{"role": "system", "content": "You classify user intents."}, 
                                         {"role": "user", "content": intent_query}])
            
            intent_text = intent_response.content.strip() if hasattr(intent_response, 'content') else str(intent_response).strip()
            
            logger.info(f"Intent classification for '{latest_message[:30]}...': {intent_text}")
            
            # Route based on intent
            if "EDIT" in intent_text.upper():
                return {"messages": messages, "next": "edit_resume"}
            else:
                return {"messages": messages, "next": "chat"}
                
        except Exception as e:
            logger.error(f"Error in router: {e}")
            # Default to chat if classification fails
            return {"messages": messages, "next": "chat"}
    
    # Function to handle resume editing operations
    def edit_resume(state: AgentState, config: Optional[RunnableConfig] = None) -> Dict:
        """Handle resume editing operations with proper parsing."""
        messages = state["messages"]
        user_id = state["user_id"]
        
        # Get the latest user message
        latest_message = None
        for msg in reversed(messages):
            if isinstance(msg, HumanMessage):
                latest_message = msg.content
                break
        
        if not latest_message:
            return {"messages": messages + [AIMessage(content="I couldn't find your request. What would you like to edit in your resume?")], "next": END}
        
        # Enhanced system prompt for resume editing
        edit_prompt = """You are a helpful Resume Copilot assistant specializing in making precise edits to resumes.
        
        Your task is to understand what changes the user wants to make to their resume and execute them precisely.
        
        Important:
        - If the user asks to add random skills, generate relevant professional skills based on their resume, not literally add 'random' as a skill
        - If they ask to add multiple skills, identify all the skills they want to add
        - Be precise in your understanding of what section they want to modify
        - Parse their request intelligently, understanding the context of resume editing
        
        First, retrieve the current resume to understand what's already there.
        Then, make the specific changes the user is requesting.
        
        User request: {user_message}
        """
        
        try:
            # Step 1: Get current resume data
            get_resume_tool = None
            for tool in resume_tools:
                if tool.name == "get_resume":
                    get_resume_tool = tool
                    break
            
            if not get_resume_tool:
                return {"messages": messages + [AIMessage(content="I couldn't find the tool to get your resume.")], "next": END}
            
            # Get current resume data
            resume_data = get_resume_tool.run(user_id)
            if "error" in resume_data:
                return {"messages": messages + [AIMessage(content=f"I couldn't retrieve your resume: {resume_data['error']}")], "next": END}
            
            # Step 2: Ask the LLM to interpret what changes are needed
            interpretation_prompt = edit_prompt.format(user_message=latest_message)
            interpretation_prompt += f"\n\nCurrent resume data: {json.dumps(resume_data, indent=2, default=_serialize_date)}\n\n"
            interpretation_prompt += "Based on the user's request and current resume, what EXACTLY needs to be changed? Be specific about the section and the changes needed."
            
            interpretation = llm.invoke([{"role": "system", "content": interpretation_prompt}])
            interpretation_text = interpretation.content if hasattr(interpretation, 'content') else str(interpretation)
            
            logger.info(f"Interpretation of edit request: {interpretation_text[:100]}...")
            
            # Step 3: Have the LLM make a precise tool call with structured data
            tool_call_prompt = f"Based on this interpretation: {interpretation_text}\n\n"
            tool_call_prompt += "You need to make the appropriate changes to the resume. Choose one of these tools:\n"
            tool_call_prompt += "1. update_resume_section - to update a specific section (skills, education, experience, projects, summary, contact)\n"
            tool_call_prompt += "2. update_resume - to update multiple sections at once\n\n"
            tool_call_prompt += "For the tool you choose, provide EXACTLY the JSON data needed for the update.\n"
            tool_call_prompt += "Example format for update_resume_section: {\"user_id\": \"<user_id>\", \"section\": \"<section_name>\", \"data\": <structured_data>}\n\n"
            tool_call_prompt += "The output should ONLY be a valid JSON object representing the tool call."
            
            # Get the tool call data
            tool_call_response = llm.invoke([{"role": "system", "content": tool_call_prompt}])
            tool_call_text = tool_call_response.content if hasattr(tool_call_response, 'content') else str(tool_call_response)
            
            # Extract the JSON from the response (might be surrounded by markdown code blocks or explanation text)
            import re
            json_match = re.search(r'```(?:json)?(.*?)```', tool_call_text, re.DOTALL)
            if json_match:
                tool_call_json = json_match.group(1).strip()
            else:
                # Try to find a JSON object without code blocks
                json_match = re.search(r'({.*})', tool_call_text, re.DOTALL)
                if json_match:
                    tool_call_json = json_match.group(1).strip()
                else:
                    tool_call_json = tool_call_text
            
            # Parse the JSON
            try:
                tool_data = json.loads(tool_call_json)
                
                # Ensure user_id is set correctly
                tool_data["user_id"] = user_id
                
                # Execute the appropriate tool
                tool_name = None
                if "section" in tool_data:
                    tool_name = "update_resume_section"
                    
                    # Fix section data structure based on section type
                    section = tool_data["section"]
                    data = tool_data["data"]
                    
                    # Get the current resume to understand existing data structure
                    for tool in resume_tools:
                        if tool.name == "get_resume":
                            current_resume = tool.run(user_id)
                            break
                    
                    # Handle list sections (projects, skills, education, experience)
                    list_sections = ["projects", "skills", "education", "experience"]
                    if section in list_sections:
                        if not isinstance(data, list):
                            # A single item was provided, but we need a list
                            if section in current_resume:
                                # Get existing items
                                existing_items = current_resume.get(section, [])
                                
                                # Check if we're updating an existing item or adding a new one
                                updated = False
                                if "name" in data:
                                    for i, item in enumerate(existing_items):
                                        if item.get("name") == data.get("name"):
                                            # Update existing item
                                            existing_items[i] = data
                                            updated = True
                                            break
                                
                                # If not updating, append the new item
                                if not updated:
                                    existing_items.append(data)
                                
                                # Update the tool data
                                tool_data["data"] = existing_items
                            else:
                                # No existing items, create a new list with this item
                                tool_data["data"] = [data]
                else:
                    tool_name = "update_resume"
                
                for tool in resume_tools:
                    if tool.name == tool_name:
                        logger.info(f"Executing {tool_name} with data: {tool_data}")
                        # Pass tool_data as a single argument, not unpacked
                        result = tool.run(tool_data)
                        
                        if result.get("status") == "success":
                            # Get a summary of what was changed
                            summary_prompt = "Explain in a friendly, conversational way what changes were just made to the resume based on this data:\n\n"
                            summary_prompt += f"Tool used: {tool_name}\n"
                            summary_prompt += f"Data: {json.dumps(tool_data, indent=2, default=_serialize_date)}\n\n"
                            summary_prompt += "Keep your response brief but specific about what was updated."
                            
                            summary_response = llm.invoke([{"role": "system", "content": summary_prompt}])
                            summary_text = summary_response.content if hasattr(summary_response, 'content') else str(summary_response)
                            
                            return {"messages": messages + [AIMessage(content=summary_text)], "next": END}
                        else:
                            return {"messages": messages + [AIMessage(content=f"I had trouble updating your resume: {result.get('message', 'Unknown error')}")], "next": END}
                
                return {"messages": messages + [AIMessage(content="I couldn't find the right tool to update your resume.")], "next": END}
                
            except json.JSONDecodeError:
                logger.error(f"Failed to parse tool call JSON: {tool_call_json}")
                return {"messages": messages + [AIMessage(content="I had trouble understanding how to update your resume. Could you be more specific about what you want to change?")], "next": END}
                
        except Exception as e:
            logger.error(f"Error in edit_resume: {e}")
            return {"messages": messages + [AIMessage(content=f"I encountered an error while trying to update your resume: {str(e)}")], "next": END}
    
    # Function for simple chat without editing
    def chat(state: AgentState, config: Optional[RunnableConfig] = None) -> Dict:
        """Handle conversational interactions about the resume without editing."""
        messages = state["messages"]
        user_id = state["user_id"]
        
        # Get the latest user message
        latest_message = None
        for msg in reversed(messages):
            if isinstance(msg, HumanMessage):
                latest_message = msg.content
                break
        
        if not latest_message:
            response = "I'm ready to help with your resume. What would you like to know?"
            return {"messages": messages + [AIMessage(content=response)], "next": END}
        
        # Format the system message with the user ID
        formatted_system = system_prompt.format(user_id=user_id)
        
        # Create a conversation history for the LLM
        conversation = []
        conversation.append({"role": "system", "content": formatted_system})
        
        # Add user/assistant message pairs to conversation
        for msg in messages:
            if isinstance(msg, HumanMessage):
                conversation.append({"role": "user", "content": msg.content})
            elif isinstance(msg, AIMessage):
                conversation.append({"role": "assistant", "content": msg.content})
        
        try:
            # Get resume data for context if needed
            get_resume_tool = None
            for tool in resume_tools:
                if tool.name == "get_resume":
                    get_resume_tool = tool
                    break
            
            if get_resume_tool:
                resume_data = get_resume_tool.run(user_id)
                # Add resume context to the system prompt if available
                if "error" not in resume_data:
                    # Use _serialize_date to handle date objects properly
                    resume_context = f"\n\nHere is the user's current resume information: {json.dumps(resume_data, indent=2, default=_serialize_date)}"
                    # Update the system message with resume context
                    conversation[0]["content"] += resume_context
            
            # Generate response
            llm_response = llm.invoke(conversation)
            
            # Extract content from the response
            if isinstance(llm_response, AIMessage):
                response_text = llm_response.content
            else:
                response_text = str(llm_response)
            
            return {"messages": messages + [AIMessage(content=response_text)], "next": END}
            
        except Exception as e:
            logger.error(f"Error in chat: {e}")
            return {"messages": messages + [AIMessage(content=f"I encountered an error while processing your request: {str(e)}")], "next": END}
    
    # Create graph with proper configuration
    workflow = StateGraph(AgentState)
    
    # Define nodes
    workflow.add_node("router", router)  # New router node to determine intent
    workflow.add_node("chat", chat)      # Handle conversational interactions
    workflow.add_node("edit_resume", edit_resume)  # Handle resume editing operations
    
    # Define edges
    workflow.add_conditional_edges(
        "router",
        lambda state: state["next"]
    )
    
    # Set entry point
    workflow.set_entry_point("router")
    
    # Compile
    app = workflow.compile()
    
    return app, checkpoint_store


class ChatSession:
    """Chat session for a user with the resume copilot agent."""
    
    def __init__(
        self,
        user_id: str,
        config: AgentConfig,
        agent_app: Runnable,
        checkpoint_store: MemorySaver
    ):
        """Initialize a chat session.
        
        Args:
            user_id: User identifier
            config: Agent configuration
            agent_app: Agent app
            checkpoint_store: Checkpoint store
        """
        self.user_id = user_id
        self.config = config
        self.agent_app = agent_app
        self.checkpoint_store = checkpoint_store
        self.thread_id = f"resume-copilot-{user_id}"
        
        # Initialize in-memory chat history
        self.chat_history = ChatMessageHistory()
    
    async def send_message(self, message: str) -> str:
        """Send a message to the agent and get a response.
        
        Args:
            message: User message
            
        Returns:
            Agent response
        """
        # Add message to history
        self.chat_history.add_message(HumanMessage(content=message))
        
        # Get all messages from history
        messages = self.chat_history.messages
        
        # Create initial state
        state = {
            "messages": messages,
            "user_id": self.user_id
        }
        
        # Configure agent run
        config = {
            "configurable": {
                "thread_id": self.thread_id
            }
        }
        
        try:
            # Run agent with checkpoints
            result = self.agent_app.with_config(config).invoke(state)
            
            # Extract response - handle potential ChatPromptValue
            if "messages" in result and result["messages"]:
                # Get the last message from the result
                last_message = result["messages"][-1]
                
                # Handle different message types
                if isinstance(last_message, AIMessage):
                    response = last_message.content
                elif isinstance(last_message, BaseMessage):
                    response = last_message.content
                elif isinstance(last_message, dict) and "content" in last_message:
                    response = last_message["content"]
                else:
                    response = str(last_message)
                
                # Add agent response to history
                self.chat_history.add_message(AIMessage(content=response))
                
                return response
            else:
                # Fallback if no messages in result
                fallback_response = "I'm sorry, I couldn't process your request properly."
                self.chat_history.add_message(AIMessage(content=fallback_response))
                return fallback_response
        except Exception as e:
            logger.error(f"Error in agent: {e}")
            error_message = f"I encountered an error: {str(e)}"
            self.chat_history.add_message(AIMessage(content=error_message))
            return error_message
