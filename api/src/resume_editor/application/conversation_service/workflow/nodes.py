import json
from typing import Dict, Any, Optional

from langchain_core.messages import AIMessage, HumanMessage
from loguru import logger

from .chains import (
    get_resume_editor_chain,
    get_intent_classification_chain,
    get_edit_interpretation_chain,
    get_tool_call_generation_chain,
    get_change_summary_chain,
    get_summarization_chain,
)
from .state import ResumeEditorState
from resume_editor.tools import ResumeStore, get_resume_tools, _serialize_date
from resume_editor.config import settings


def conversation_node(state: ResumeEditorState) -> Dict[str, Any]:
    """Main conversation node for resume editing interactions."""
    messages = state["messages"]
    user_id = state["user_id"]
    editor_name = state["editor_name"]
    editor_specialty = state["editor_specialty"]
    editor_approach = state["editor_approach"]
    editor_focus_areas = state["editor_focus_areas"]

    # Get the latest user message
    latest_message = None
    for msg in reversed(messages):
        if isinstance(msg, HumanMessage):
            latest_message = msg.content
            break

    if not latest_message:
        response = f"Hello! I'm {editor_name}, your resume editor specializing in {editor_specialty}. How can I help you improve your resume today?"
        return {"messages": [AIMessage(content=response)]}

    # Get resume editor chain
    chain = get_resume_editor_chain()

    # Format the conversation context
    context = {
        "user_id": user_id,
        "editor_name": editor_name,
        "editor_specialty": editor_specialty,
        "editor_approach": editor_approach,
        "editor_focus_areas": editor_focus_areas,
        "messages": messages,
    }

    try:
        # Generate response
        response = chain.invoke(context)
        content = response.content if hasattr(response, 'content') else str(response)

        return {"messages": [AIMessage(content=content)]}

    except Exception as e:
        logger.error(f"Error in conversation node: {e}")
        error_response = "I apologize, but I encountered an error. Please try rephrasing your request."
        return {"messages": [AIMessage(content=error_response)]}


def intent_classification_node(state: ResumeEditorState) -> Dict[str, Any]:
    """Classify user intent to determine workflow path."""
    messages = state["messages"]

    # Get the latest user message
    latest_message = None
    for msg in reversed(messages):
        if isinstance(msg, HumanMessage):
            latest_message = msg.content
            break

    if not latest_message:
        return {"intent": "chat"}

    try:
        # Use intent classification chain
        chain = get_intent_classification_chain()
        response = chain.invoke({"user_message": latest_message})

        intent_text = response.content if hasattr(response, 'content') else str(response)
        intent = "edit" if "EDIT" in intent_text.upper() else "chat"

        logger.info(f"Intent classification for '{latest_message[:30]}...': {intent}")
        return {"intent": intent}

    except Exception as e:
        logger.error(f"Error in intent classification: {e}")
        return {"intent": "chat"}  # Default to chat on error


def resume_editing_node(state: ResumeEditorState) -> Dict[str, Any]:
    """Handle resume editing operations."""
    messages = state["messages"]
    user_id = state["user_id"]

    # Get the latest user message
    latest_message = None
    for msg in reversed(messages):
        if isinstance(msg, HumanMessage):
            latest_message = msg.content
            break

    if not latest_message:
        response = "I couldn't find your request. What would you like to edit in your resume?"
        return {"messages": [AIMessage(content=response)]}

    try:
        # Initialize resume tools
        resume_store = ResumeStore(settings.RESUME_DATA_PATH)
        resume_tools = get_resume_tools(resume_store)

        # Get current resume data
        get_resume_tool = next((tool for tool in resume_tools if tool.name == "get_resume"), None)
        if not get_resume_tool:
            response = "I'm having trouble accessing your resume. Please try again."
            return {"messages": [AIMessage(content=response)]}

        resume_data = get_resume_tool.run(user_id)
        if "error" in resume_data:
            response = "I couldn't find your resume. Would you like me to help you create one?"
            return {"messages": [AIMessage(content=response)]}

        # Interpret the edit request
        interpretation_chain = get_edit_interpretation_chain()
        interpretation_response = interpretation_chain.invoke({
            "user_message": latest_message,
            "resume_data": json.dumps(resume_data, indent=2, default=_serialize_date)
        })

        interpretation = interpretation_response.content if hasattr(interpretation_response, 'content') else str(interpretation_response)

        # Generate tool call
        tool_call_chain = get_tool_call_generation_chain()
        tool_call_response = tool_call_chain.invoke({
            "interpretation": interpretation,
            "user_id": user_id
        })

        tool_call_text = tool_call_response.content if hasattr(tool_call_response, 'content') else str(tool_call_response)

        # Parse and execute tool call
        try:
            # Extract JSON from response
            tool_call_data = json.loads(tool_call_text.strip())

            # Determine which tool to use
            if "section" in tool_call_data:
                tool_name = "update_resume_section"
                update_tool = next((tool for tool in resume_tools if tool.name == tool_name), None)
            else:
                tool_name = "update_resume"
                update_tool = next((tool for tool in resume_tools if tool.name == tool_name), None)

            if update_tool:
                result = update_tool.run(tool_call_data)

                if result.get("status") == "success":
                    # Generate summary of changes
                    summary_chain = get_change_summary_chain()
                    summary_response = summary_chain.invoke({
                        "tool_name": tool_name,
                        "tool_data": json.dumps(tool_call_data, indent=2, default=_serialize_date)
                    })

                    summary_text = summary_response.content if hasattr(summary_response, 'content') else str(summary_response)
                    return {"messages": [AIMessage(content=summary_text)]}
                else:
                    return {"messages": [AIMessage(content=f"I encountered an issue: {result.get('message', 'Unknown error')}")]}
            else:
                return {"messages": [AIMessage(content="I couldn't find the appropriate tool to make that change.")]}

        except json.JSONDecodeError:
            return {"messages": [AIMessage(content="I had trouble understanding your request. Could you please be more specific about what you'd like to change?")]}

    except Exception as e:
        logger.error(f"Error in resume editing node: {e}")
        return {"messages": [AIMessage(content="I encountered an error while trying to edit your resume. Please try again.")]}


def summarize_conversation_node(state: ResumeEditorState) -> Dict[str, Any]:
    """Summarize the conversation to manage token usage."""
    messages = state["messages"]

    if len(messages) < 10:  # Don't summarize short conversations
        return {"summary": ""}

    try:
        # Use summarization chain
        chain = get_summarization_chain()

        # Convert messages to text format
        conversation_text = ""
        for msg in messages:
            if isinstance(msg, HumanMessage):
                conversation_text += f"User: {msg.content}\n"
            elif isinstance(msg, AIMessage):
                conversation_text += f"Assistant: {msg.content}\n"

        response = chain.invoke({"conversation": conversation_text})
        summary = response.content if hasattr(response, 'content') else str(response)

        return {"summary": summary}

    except Exception as e:
        logger.error(f"Error in summarization: {e}")
        return {"summary": ""}


def connector_node(state: ResumeEditorState) -> Dict[str, Any]:
    """Connector node to manage workflow transitions."""
    # This node helps manage the flow between different parts of the workflow
    # It can be used to make decisions about whether to summarize, continue, or end
    return {}
