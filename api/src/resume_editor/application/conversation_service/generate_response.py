import uuid
from typing import Any, <PERSON><PERSON><PERSON><PERSON>ator, <PERSON>

from langchain_core.messages import AIMessage, AIMessageChunk, HumanMessage
from langgraph.checkpoint.mongodb.aio import AsyncMongoDBSaver
from opik.integrations.langchain import OpikTracer

from resume_editor.config import settings
from .workflow.graph import create_resume_editor_workflow_graph
from .workflow.state import ResumeEditorState


def __format_messages(messages: str | list[str] | list[dict[str, Any]]) -> list[HumanMessage | AIMessage]:
    """Format messages into the correct format for the workflow."""
    if isinstance(messages, str):
        return [HumanMessage(content=messages)]
    elif isinstance(messages, list):
        formatted_messages = []
        for msg in messages:
            if isinstance(msg, str):
                formatted_messages.append(HumanMessage(content=msg))
            elif isinstance(msg, dict):
                if msg.get("role") == "user":
                    formatted_messages.append(HumanMessage(content=msg.get("content", "")))
                elif msg.get("role") == "assistant":
                    formatted_messages.append(AIMessage(content=msg.get("content", "")))
        return formatted_messages
    return []


async def get_response(
    messages: str | list[str] | list[dict[str, Any]],
    user_id: str,
    editor_id: str,
    editor_name: str,
    editor_specialty: str,
    editor_approach: str,
    editor_focus_areas: list[str],
    user_context: str = "",
    session_goals: list[str] = None,
    new_thread: bool = False,
) -> tuple[str, ResumeEditorState]:
    """Run a resume editing conversation through the workflow graph.

    Args:
        messages: Initial message to start the conversation.
        user_id: Unique identifier for the user.
        editor_id: Unique identifier for the resume editor.
        editor_name: Name of the resume editor.
        editor_specialty: Editor's specialty area.
        editor_approach: Editor's approach and methodology.
        editor_focus_areas: List of editor's focus areas.
        user_context: Additional context about the user's career situation.
        session_goals: Goals for the editing session.
        new_thread: Whether to create a new conversation thread.

    Returns:
        Tuple of (response_content, final_state)

    Raises:
        RuntimeError: If there's an error running the conversation workflow.
    """
    if session_goals is None:
        session_goals = []

    graph_builder = create_resume_editor_workflow_graph()

    try:
        async with AsyncMongoDBSaver.from_conn_string(
            conn_string=settings.MONGO_URI,
            db_name=settings.MONGO_DB_NAME,
            checkpoint_collection_name=settings.MONGO_RESUME_STATE_CHECKPOINT_COLLECTION,
            writes_collection_name=settings.MONGO_RESUME_STATE_WRITES_COLLECTION,
        ) as checkpointer:
            graph = graph_builder.compile(checkpointer=checkpointer)
            opik_tracer = OpikTracer(graph=graph.get_graph(xray=True))

            # Create thread ID with user_id and editor_id for multi-user support
            thread_id = (
                f"{user_id}_{editor_id}" if not new_thread
                else f"{user_id}_{editor_id}_{uuid.uuid4()}"
            )
            config = {
                "configurable": {"thread_id": thread_id},
                "callbacks": [opik_tracer],
            }

            output_state = await graph.ainvoke(
                input={
                    "messages": __format_messages(messages=messages),
                    "user_id": user_id,
                    "editor_id": editor_id,
                    "editor_name": editor_name,
                    "editor_specialty": editor_specialty,
                    "editor_approach": editor_approach,
                    "editor_focus_areas": editor_focus_areas,
                    "user_context": user_context,
                    "session_goals": session_goals,
                },
                config=config,
            )
        last_message = output_state["messages"][-1]
        return last_message.content, ResumeEditorState(**output_state)
    except Exception as e:
        raise RuntimeError(f"Error running resume editor conversation workflow: {str(e)}") from e


async def get_streaming_response(
    messages: str | list[str] | list[dict[str, Any]],
    user_id: str,
    editor_id: str,
    editor_name: str,
    editor_specialty: str,
    editor_approach: str,
    editor_focus_areas: list[str],
    user_context: str = "",
    session_goals: list[str] = None,
    new_thread: bool = False,
) -> AsyncGenerator[str, None]:
    """Run a streaming resume editing conversation through the workflow graph.

    Args:
        messages: Initial message to start the conversation.
        user_id: Unique identifier for the user.
        editor_id: Unique identifier for the resume editor.
        editor_name: Name of the resume editor.
        editor_specialty: Editor's specialty area.
        editor_approach: Editor's approach and methodology.
        editor_focus_areas: List of editor's focus areas.
        user_context: Additional context about the user's career situation.
        session_goals: Goals for the editing session.
        new_thread: Whether to create a new conversation thread.

    Yields:
        Chunks of the response as they become available.

    Raises:
        RuntimeError: If there's an error running the conversation workflow.
    """
    if session_goals is None:
        session_goals = []

    graph_builder = create_resume_editor_workflow_graph()

    try:
        async with AsyncMongoDBSaver.from_conn_string(
            conn_string=settings.MONGO_URI,
            db_name=settings.MONGO_DB_NAME,
            checkpoint_collection_name=settings.MONGO_RESUME_STATE_CHECKPOINT_COLLECTION,
            writes_collection_name=settings.MONGO_RESUME_STATE_WRITES_COLLECTION,
        ) as checkpointer:
            graph = graph_builder.compile(checkpointer=checkpointer)
            opik_tracer = OpikTracer(graph=graph.get_graph(xray=True))

            # Create thread ID with user_id and editor_id for multi-user support
            thread_id = (
                f"{user_id}_{editor_id}" if not new_thread
                else f"{user_id}_{editor_id}_{uuid.uuid4()}"
            )
            config = {
                "configurable": {"thread_id": thread_id},
                "callbacks": [opik_tracer],
            }

            async for chunk in graph.astream(
                input={
                    "messages": __format_messages(messages=messages),
                    "user_id": user_id,
                    "editor_id": editor_id,
                    "editor_name": editor_name,
                    "editor_specialty": editor_specialty,
                    "editor_approach": editor_approach,
                    "editor_focus_areas": editor_focus_areas,
                    "user_context": user_context,
                    "session_goals": session_goals,
                },
                config=config,
                stream_mode="messages",
            ):
                if chunk[1]["langgraph_node"] == "conversation_node" and isinstance(
                    chunk[0], AIMessageChunk
                ):
                    yield chunk[0].content

    except Exception as e:
        raise RuntimeError(
            f"Error running streaming resume editor conversation workflow: {str(e)}"
        ) from e
