from contextlib import asynccontextmanager
from typing import Dict, List, Optional, Any

from fastapi import FastAP<PERSON>, HTTPException, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from opik.integrations.langchain import OpikTracer
from pydantic import BaseModel

from resume_editor.application.conversation_service.generate_response import (
    get_response,
    get_streaming_response,
)
from resume_editor.application.conversation_service.reset_conversation import (
    reset_conversation_state,
)
from resume_editor.config import settings
from resume_editor.domain.resume_editor_factory import ResumeEditorFactory
from resume_editor.tools import ResumeStore
from resume_editor.utils import initialize_resume, copy_sample_resume
from common.infrastructure.opik_utils import configure

configure(settings.COMET_API_KEY, settings.RESUME_EDITOR_PROJECT)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Handles startup and shutdown events for the Resume Editor API."""
    # Startup code (if any) goes here
    yield
    # Shutdown code goes here
    opik_tracer = OpikTracer()
    opik_tracer.flush()


app = FastAPI(
    title="Resume Editor API",
    description="API for resume editing agents with multi-user support",
    version="1.0.0",
    lifespan=lifespan
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# Pydantic models for the API
class ChatMessage(BaseModel):
    """Request model for chat messages."""
    
    user_id: str
    editor_id: str
    message: str
    user_context: Optional[str] = ""
    session_goals: Optional[List[str]] = []


class ResumeRequest(BaseModel):
    """Request model for creating or updating a resume."""
    
    user_id: str
    resume_data: Dict[str, Any]


class ResumeResponse(BaseModel):
    """Response model for resume operations."""
    
    status: str
    message: str
    data: Optional[Dict[str, Any]] = None


class InitResumeRequest(BaseModel):
    """Request model for initializing a new resume."""
    
    user_id: str
    name: str
    email: str
    title: Optional[str] = None


class CopySampleRequest(BaseModel):
    """Request model for copying the sample resume."""
    
    user_id: str


@app.post("/chat")
async def chat(chat_message: ChatMessage):
    """Chat endpoint for resume editing conversations."""
    try:
        editor_factory = ResumeEditorFactory()
        editor = editor_factory.get_editor(chat_message.editor_id)

        response, _ = await get_response(
            messages=chat_message.message,
            user_id=chat_message.user_id,
            editor_id=chat_message.editor_id,
            editor_name=editor.name,
            editor_specialty=editor.specialty,
            editor_approach=editor.approach,
            editor_focus_areas=editor.focus_areas,
            user_context=chat_message.user_context,
            session_goals=chat_message.session_goals,
        )
        return {"response": response}
    except Exception as e:
        opik_tracer = OpikTracer()
        opik_tracer.flush()

        raise HTTPException(status_code=500, detail=str(e))


@app.websocket("/chat/stream")
async def chat_stream(websocket: WebSocket):
    """WebSocket endpoint for streaming resume editing conversations."""
    await websocket.accept()
    
    try:
        while True:
            # Receive message from client
            data = await websocket.receive_json()
            
            # Validate required fields
            required_fields = ["user_id", "editor_id", "message"]
            if not all(field in data for field in required_fields):
                await websocket.send_json({
                    "error": f"Missing required fields: {required_fields}"
                })
                continue
            
            try:
                editor_factory = ResumeEditorFactory()
                editor = editor_factory.get_editor(data["editor_id"])
                
                # Stream the response
                async for chunk in get_streaming_response(
                    messages=data["message"],
                    user_id=data["user_id"],
                    editor_id=data["editor_id"],
                    editor_name=editor.name,
                    editor_specialty=editor.specialty,
                    editor_approach=editor.approach,
                    editor_focus_areas=editor.focus_areas,
                    user_context=data.get("user_context", ""),
                    session_goals=data.get("session_goals", []),
                ):
                    await websocket.send_json({"chunk": chunk})
                
                # Send end-of-stream marker
                await websocket.send_json({"end": True})
                
            except Exception as e:
                await websocket.send_json({"error": str(e)})
                
    except WebSocketDisconnect:
        pass
    except Exception as e:
        await websocket.send_json({"error": str(e)})
    finally:
        opik_tracer = OpikTracer()
        opik_tracer.flush()


@app.post("/reset")
async def reset_conversation(user_id: str, editor_id: str):
    """Reset conversation state for a user and editor."""
    try:
        success = await reset_conversation_state(user_id, editor_id)
        if success:
            return {"status": "success", "message": "Conversation reset successfully"}
        else:
            raise HTTPException(status_code=500, detail="Failed to reset conversation")
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/editors")
async def get_available_editors():
    """Get list of available resume editors."""
    try:
        editor_factory = ResumeEditorFactory()
        available_editors = editor_factory.get_available_editors()
        
        editors_info = []
        for editor_id in available_editors:
            editor = editor_factory.get_editor(editor_id)
            editors_info.append({
                "id": editor.id,
                "name": editor.name,
                "specialty": editor.specialty,
                "focus_areas": editor.focus_areas
            })
        
        return {"editors": editors_info}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/resume/init", response_model=ResumeResponse)
async def init_resume(request: InitResumeRequest):
    """Initialize a new resume for a user."""
    try:
        result = initialize_resume(
            user_id=request.user_id,
            name=request.name,
            email=request.email,
            title=request.title,
            data_path=settings.RESUME_DATA_PATH,
        )
        return ResumeResponse(**result)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/resume/copy-sample", response_model=ResumeResponse)
async def copy_sample(request: CopySampleRequest):
    """Copy the sample resume for a user."""
    try:
        result = copy_sample_resume(
            user_id=request.user_id,
            data_path=settings.RESUME_DATA_PATH,
        )
        return ResumeResponse(**result)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/resume/{user_id}")
async def get_resume(user_id: str):
    """Get resume data for a user."""
    try:
        resume_store = ResumeStore(settings.RESUME_DATA_PATH)
        resume_data = resume_store.get_resume(user_id)
        
        if "error" in resume_data:
            raise HTTPException(status_code=404, detail=resume_data["error"])
        
        return {"status": "success", "data": resume_data}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "healthy", "service": "resume_editor"}


if __name__ == "__main__":
    import uvicorn

    uvicorn.run(app, host="0.0.0.0", port=8002)
