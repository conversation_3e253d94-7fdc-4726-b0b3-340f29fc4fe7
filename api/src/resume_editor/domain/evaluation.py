"""Domain models for resume editor evaluation."""

from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field


class ResumeEvaluationCriteria(BaseModel):
    """Criteria for evaluating resume quality."""
    
    ats_compatibility: float = Field(description="ATS compatibility score (0-100)")
    content_quality: float = Field(description="Content quality score (0-100)")
    formatting: float = Field(description="Formatting score (0-100)")
    completeness: float = Field(description="Completeness score (0-100)")
    keyword_optimization: float = Field(description="Keyword optimization score (0-100)")


class ResumeEvaluationResult(BaseModel):
    """Result of resume evaluation."""
    
    user_id: str = Field(description="User identifier")
    overall_score: float = Field(description="Overall resume score (0-100)")
    criteria_scores: ResumeEvaluationCriteria = Field(description="Individual criteria scores")
    strengths: List[str] = Field(description="Resume strengths")
    weaknesses: List[str] = Field(description="Areas for improvement")
    recommendations: List[str] = Field(description="Specific recommendations")
    ats_issues: List[str] = Field(description="ATS compatibility issues")
    missing_sections: List[str] = Field(description="Missing resume sections")


class EditingSessionEvaluation(BaseModel):
    """Evaluation of an editing session."""
    
    session_id: str = Field(description="Session identifier")
    user_id: str = Field(description="User identifier")
    editor_id: str = Field(description="Editor identifier")
    changes_made: List[str] = Field(description="List of changes made during session")
    improvement_score: float = Field(description="Improvement score (0-100)")
    user_satisfaction: Optional[float] = Field(description="User satisfaction score (0-100)")
    session_duration: Optional[int] = Field(description="Session duration in minutes")
    goals_achieved: List[str] = Field(description="Session goals that were achieved")


class ResumeQualityMetrics(BaseModel):
    """Metrics for measuring resume quality."""
    
    word_count: int = Field(description="Total word count")
    section_count: int = Field(description="Number of sections")
    bullet_points: int = Field(description="Number of bullet points")
    quantified_achievements: int = Field(description="Number of quantified achievements")
    action_verbs: int = Field(description="Number of action verbs used")
    keywords_matched: int = Field(description="Number of relevant keywords")
    readability_score: float = Field(description="Readability score")
    
    
class ATSCompatibilityCheck(BaseModel):
    """ATS compatibility assessment."""
    
    file_format_compatible: bool = Field(description="File format is ATS-compatible")
    font_compatible: bool = Field(description="Font is ATS-compatible")
    formatting_simple: bool = Field(description="Formatting is simple and clean")
    sections_properly_labeled: bool = Field(description="Sections are properly labeled")
    contact_info_parseable: bool = Field(description="Contact info is parseable")
    dates_formatted_correctly: bool = Field(description="Dates are formatted correctly")
    no_graphics_or_images: bool = Field(description="No graphics or images present")
    keywords_present: bool = Field(description="Relevant keywords are present")
